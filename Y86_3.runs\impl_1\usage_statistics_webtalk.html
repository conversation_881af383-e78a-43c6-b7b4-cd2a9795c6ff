<HTML><HEAD><TITLE>Device Usage Statistics Report</TITLE></HEAD>
<BODY TEXT='#000000' BGCOLOR='#FFFFFF' LINK='#0000EE' VLINK='#551A8B' ALINK='#FF0000'><H3>Device Usage Page (usage_statistics_webtalk.html)</H3>This HTML page displays the device usage statistics that will be sent to Xilinx.<BR>To see the actual file transmitted to Xilinx, please click <A HREF="./usage_statistics_webtalk.xml">here</A>.<BR><BR><HR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>software_version_and_target_device</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>beta</B></TD><TD>FALSE</TD>
  <TD BGCOLOR='#DBE5F1'><B>build_version</B></TD><TD>2405991</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>date_generated</B></TD><TD>Wed Jun  4 00:33:16 2025</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_platform</B></TD><TD>WIN64</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>product_version</B></TD><TD>Vivado v2018.3 (64-bit)</TD>
  <TD BGCOLOR='#DBE5F1'><B>project_id</B></TD><TD>f4ec1bf26e7946b086b59777a922266e</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>project_iteration</B></TD><TD>2</TD>
  <TD BGCOLOR='#DBE5F1'><B>random_id</B></TD><TD>1df8074c817b5f1cb46b2114e3a8a019</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>registration_id</B></TD><TD>1df8074c817b5f1cb46b2114e3a8a019</TD>
  <TD BGCOLOR='#DBE5F1'><B>route_design</B></TD><TD>TRUE</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_device</B></TD><TD>xc7a100t</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_family</B></TD><TD>artix7</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_package</B></TD><TD>fgg484</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_speed</B></TD><TD>-1</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>tool_flow</B></TD><TD>Vivado</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>user_environment</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>cpu_name</B></TD><TD>12th Gen Intel(R) Core(TM) i7-12700H</TD>
  <TD BGCOLOR='#DBE5F1'><B>cpu_speed</B></TD><TD>2688 MHz</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>os_name</B></TD><TD>Microsoft Windows 8 or later , 64-bit</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_release</B></TD><TD>major release  (build 9200)</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>system_ram</B></TD><TD>16.000 GB</TD>
  <TD BGCOLOR='#DBE5F1'><B>total_processors</B></TD><TD>1</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>vivado_usage</B></TD></TR>
<TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>gui_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>basedialog_ok=7</TD>
   <TD>basedialog_yes=3</TD>
   <TD>createconstraintsfilepanel_file_location=1</TD>
   <TD>createconstraintsfilepanel_file_name=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>filesetpanel_file_set_panel_tree=12</TD>
   <TD>flownavigatortreepanel_flow_navigator_tree=11</TD>
   <TD>fpgachooser_fpga_table=1</TD>
   <TD>mainmenumgr_edit=6</TD>
</TR><TR ALIGN='LEFT'>   <TD>mainmenumgr_file=6</TD>
   <TD>mainmenumgr_flow=2</TD>
   <TD>mainmenumgr_project=1</TD>
   <TD>maintoolbarmgr_run=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>optionsview_close=1</TD>
   <TD>pacommandnames_auto_connect_target=1</TD>
   <TD>pacommandnames_new_project=1</TD>
   <TD>pacommandnames_open_hardware_manager=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_simulation_live_run_all=5</TD>
   <TD>pacommandnames_simulation_relaunch=1</TD>
   <TD>pacommandnames_simulation_run_behavioral=1</TD>
   <TD>paviews_code=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>paviews_project_summary=3</TD>
   <TD>programfpgadialog_program=1</TD>
   <TD>projectnamechooser_project_name=1</TD>
   <TD>projecttab_reload=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>rdiviews_waveform_viewer=5</TD>
   <TD>signaltreepanel_signal_tree_table=48</TD>
   <TD>simulationobjectspanel_simulation_objects_tree_table=27</TD>
   <TD>simulationscopespanel_simulate_scope_table=6</TD>
</TR><TR ALIGN='LEFT'>   <TD>srcchooserpanel_add_hdl_and_netlist_files_to_your_project=2</TD>
   <TD>srcchooserpanel_add_or_create_source_file=2</TD>
   <TD>syntheticagettingstartedview_recent_projects=2</TD>
   <TD>waveformnametree_waveform_name_tree=7</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>java_command_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>addsources=2</TD>
   <TD>autoconnecttarget=1</TD>
   <TD>launchprogramfpga=1</TD>
   <TD>newproject=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>openhardwaremanager=3</TD>
   <TD>openrecenttarget=1</TD>
   <TD>programdevice=1</TD>
   <TD>runbitgen=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>runsynthesis=1</TD>
   <TD>savedesign=2</TD>
   <TD>showview=1</TD>
   <TD>simulationrelaunch=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>simulationrun=1</TD>
   <TD>simulationrunall=6</TD>
   <TD>timingconstraintswizard=1</TD>
   <TD>viewtasksynthesis=1</TD>
</TR>  </TABLE>
</TR><TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>other_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>guimode=3</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>project_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>constraintsetcount=1</TD>
   <TD>core_container=false</TD>
   <TD>currentimplrun=impl_1</TD>
   <TD>currentsynthesisrun=synth_1</TD>
</TR><TR ALIGN='LEFT'>   <TD>default_library=xil_defaultlib</TD>
   <TD>designmode=RTL</TD>
   <TD>export_simulation_activehdl=0</TD>
   <TD>export_simulation_ies=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_modelsim=0</TD>
   <TD>export_simulation_questa=0</TD>
   <TD>export_simulation_riviera=0</TD>
   <TD>export_simulation_vcs=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_xsim=0</TD>
   <TD>implstrategy=Vivado Implementation Defaults</TD>
   <TD>launch_simulation_activehdl=0</TD>
   <TD>launch_simulation_ies=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_modelsim=0</TD>
   <TD>launch_simulation_questa=0</TD>
   <TD>launch_simulation_riviera=0</TD>
   <TD>launch_simulation_vcs=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_xsim=3</TD>
   <TD>simulator_language=Mixed</TD>
   <TD>srcsetcount=1</TD>
   <TD>synthesisstrategy=Vivado Synthesis Defaults</TD>
</TR><TR ALIGN='LEFT'>   <TD>target_language=Verilog</TD>
   <TD>target_simulator=XSim</TD>
   <TD>totalimplruns=1</TD>
   <TD>totalsynthesisruns=1</TD>
</TR>  </TABLE>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>unisim_transformation</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>post_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg=2</TD>
    <TD>carry4=15</TD>
    <TD>fdce=60</TD>
    <TD>fdre=2291</TD>
</TR><TR ALIGN='LEFT'>    <TD>gnd=4</TD>
    <TD>ibuf=2</TD>
    <TD>lut1=3</TD>
    <TD>lut2=243</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut3=403</TD>
    <TD>lut4=665</TD>
    <TD>lut5=710</TD>
    <TD>lut6=2111</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf7=513</TD>
    <TD>muxf8=8</TD>
    <TD>obuf=16</TD>
    <TD>vcc=4</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>pre_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg=2</TD>
    <TD>carry4=15</TD>
    <TD>fdce=60</TD>
    <TD>fdre=2291</TD>
</TR><TR ALIGN='LEFT'>    <TD>gnd=4</TD>
    <TD>ibuf=2</TD>
    <TD>lut1=3</TD>
    <TD>lut2=243</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut3=403</TD>
    <TD>lut4=665</TD>
    <TD>lut5=710</TD>
    <TD>lut6=2111</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf7=513</TD>
    <TD>muxf8=8</TD>
    <TD>obuf=16</TD>
    <TD>vcc=4</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_drc</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-internal=default::[not_specified]</TD>
    <TD>-internal_only=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-name=default::[not_specified]</TD>
    <TD>-no_waivers=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
    <TD>-ruledecks=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-upgrade_cw=default::[not_specified]</TD>
    <TD>-waived=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>cfgbvs-1=1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_methodology</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-slack_lesser_than=default::[not_specified]</TD>
    <TD>-waived=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>timing-17=1000</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_power</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-advisory=default::[not_specified]</TD>
    <TD>-append=default::[not_specified]</TD>
    <TD>-file=[specified]</TD>
    <TD>-format=default::text</TD>
</TR><TR ALIGN='LEFT'>    <TD>-hier=default::power</TD>
    <TD>-hierarchical_depth=default::4</TD>
    <TD>-l=default::[not_specified]</TD>
    <TD>-name=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-no_propagation=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
    <TD>-rpx=[specified]</TD>
    <TD>-verbose=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-vid=default::[not_specified]</TD>
    <TD>-xpe=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>airflow=250 (LFM)</TD>
    <TD>ambient_temp=25.0 (C)</TD>
    <TD>bi-dir_toggle=12.500000</TD>
    <TD>bidir_output_enable=1.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>board_layers=12to15 (12 to 15 Layers)</TD>
    <TD>board_selection=medium (10&quot;x10&quot;)</TD>
    <TD>confidence_level_clock_activity=Low</TD>
    <TD>confidence_level_design_state=High</TD>
</TR><TR ALIGN='LEFT'>    <TD>confidence_level_device_models=High</TD>
    <TD>confidence_level_internal_activity=Medium</TD>
    <TD>confidence_level_io_activity=Low</TD>
    <TD>confidence_level_overall=Low</TD>
</TR><TR ALIGN='LEFT'>    <TD>customer=TBD</TD>
    <TD>customer_class=TBD</TD>
    <TD>devstatic=0.099438</TD>
    <TD>die=xc7a100tfgg484-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>dsp_output_toggle=12.500000</TD>
    <TD>dynamic=1.245434</TD>
    <TD>effective_thetaja=2.7</TD>
    <TD>enable_probability=0.990000</TD>
</TR><TR ALIGN='LEFT'>    <TD>family=artix7</TD>
    <TD>ff_toggle=12.500000</TD>
    <TD>flow_state=routed</TD>
    <TD>heatsink=medium (Medium Profile)</TD>
</TR><TR ALIGN='LEFT'>    <TD>i/o=0.088438</TD>
    <TD>input_toggle=12.500000</TD>
    <TD>junction_temp=28.6 (C)</TD>
    <TD>logic=0.603849</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtavcc_dynamic_current=0.000000</TD>
    <TD>mgtavcc_static_current=0.000000</TD>
    <TD>mgtavcc_total_current=0.000000</TD>
    <TD>mgtavcc_voltage=1.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>mgtavtt_dynamic_current=0.000000</TD>
    <TD>mgtavtt_static_current=0.000000</TD>
    <TD>mgtavtt_total_current=0.000000</TD>
    <TD>mgtavtt_voltage=1.200000</TD>
</TR><TR ALIGN='LEFT'>    <TD>netlist_net_matched=NA</TD>
    <TD>off-chip_power=0.000000</TD>
    <TD>on-chip_power=1.344872</TD>
    <TD>output_enable=1.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>output_load=5.000000</TD>
    <TD>output_toggle=12.500000</TD>
    <TD>package=fgg484</TD>
    <TD>pct_clock_constrained=3.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>pct_inputs_defined=0</TD>
    <TD>platform=nt64</TD>
    <TD>process=typical</TD>
    <TD>ram_enable=50.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>ram_write=50.000000</TD>
    <TD>read_saif=False</TD>
    <TD>set/reset_probability=0.000000</TD>
    <TD>signal_rate=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>signals=0.553147</TD>
    <TD>simulation_file=None</TD>
    <TD>speedgrade=-1</TD>
    <TD>static_prob=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>temp_grade=commercial</TD>
    <TD>thetajb=6.8 (C/W)</TD>
    <TD>thetasa=4.6 (C/W)</TD>
    <TD>toggle_rate=False</TD>
</TR><TR ALIGN='LEFT'>    <TD>user_board_temp=25.0 (C)</TD>
    <TD>user_effective_thetaja=2.7</TD>
    <TD>user_junc_temp=28.6 (C)</TD>
    <TD>user_thetajb=6.8 (C/W)</TD>
</TR><TR ALIGN='LEFT'>    <TD>user_thetasa=4.6 (C/W)</TD>
    <TD>vccadc_dynamic_current=0.000000</TD>
    <TD>vccadc_static_current=0.020000</TD>
    <TD>vccadc_total_current=0.020000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccadc_voltage=1.800000</TD>
    <TD>vccaux_dynamic_current=0.003094</TD>
    <TD>vccaux_io_dynamic_current=0.000000</TD>
    <TD>vccaux_io_static_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccaux_io_total_current=0.000000</TD>
    <TD>vccaux_io_voltage=1.800000</TD>
    <TD>vccaux_static_current=0.018312</TD>
    <TD>vccaux_total_current=0.021406</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccaux_voltage=1.800000</TD>
    <TD>vccbram_dynamic_current=0.000000</TD>
    <TD>vccbram_static_current=0.000280</TD>
    <TD>vccbram_total_current=0.000280</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccbram_voltage=1.000000</TD>
    <TD>vccint_dynamic_current=1.160997</TD>
    <TD>vccint_static_current=0.016996</TD>
    <TD>vccint_total_current=1.177993</TD>
</TR><TR ALIGN='LEFT'>    <TD>vccint_voltage=1.000000</TD>
    <TD>vcco12_dynamic_current=0.000000</TD>
    <TD>vcco12_static_current=0.000000</TD>
    <TD>vcco12_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco12_voltage=1.200000</TD>
    <TD>vcco135_dynamic_current=0.000000</TD>
    <TD>vcco135_static_current=0.000000</TD>
    <TD>vcco135_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco135_voltage=1.350000</TD>
    <TD>vcco15_dynamic_current=0.000000</TD>
    <TD>vcco15_static_current=0.000000</TD>
    <TD>vcco15_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco15_voltage=1.500000</TD>
    <TD>vcco18_dynamic_current=0.000000</TD>
    <TD>vcco18_static_current=0.000000</TD>
    <TD>vcco18_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco18_voltage=1.800000</TD>
    <TD>vcco25_dynamic_current=0.000000</TD>
    <TD>vcco25_static_current=0.000000</TD>
    <TD>vcco25_total_current=0.000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco25_voltage=2.500000</TD>
    <TD>vcco33_dynamic_current=0.023899</TD>
    <TD>vcco33_static_current=0.004000</TD>
    <TD>vcco33_total_current=0.027899</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcco33_voltage=3.300000</TD>
    <TD>version=2018.3</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_utilization</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>clocking</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufgctrl_available=32</TD>
    <TD>bufgctrl_fixed=0</TD>
    <TD>bufgctrl_used=2</TD>
    <TD>bufgctrl_util_percentage=6.25</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufhce_available=96</TD>
    <TD>bufhce_fixed=0</TD>
    <TD>bufhce_used=0</TD>
    <TD>bufhce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufio_available=24</TD>
    <TD>bufio_fixed=0</TD>
    <TD>bufio_used=0</TD>
    <TD>bufio_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufmrce_available=12</TD>
    <TD>bufmrce_fixed=0</TD>
    <TD>bufmrce_used=0</TD>
    <TD>bufmrce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufr_available=24</TD>
    <TD>bufr_fixed=0</TD>
    <TD>bufr_used=0</TD>
    <TD>bufr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>mmcme2_adv_available=6</TD>
    <TD>mmcme2_adv_fixed=0</TD>
    <TD>mmcme2_adv_used=0</TD>
    <TD>mmcme2_adv_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv_available=6</TD>
    <TD>plle2_adv_fixed=0</TD>
    <TD>plle2_adv_used=0</TD>
    <TD>plle2_adv_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>dsp</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>dsps_available=240</TD>
    <TD>dsps_fixed=0</TD>
    <TD>dsps_used=0</TD>
    <TD>dsps_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>io_standard</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>blvds_25=0</TD>
    <TD>diff_hstl_i=0</TD>
    <TD>diff_hstl_i_18=0</TD>
    <TD>diff_hstl_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hstl_ii_18=0</TD>
    <TD>diff_hsul_12=0</TD>
    <TD>diff_mobile_ddr=0</TD>
    <TD>diff_sstl135=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl135_r=0</TD>
    <TD>diff_sstl15=0</TD>
    <TD>diff_sstl15_r=0</TD>
    <TD>diff_sstl18_i=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl18_ii=0</TD>
    <TD>hstl_i=0</TD>
    <TD>hstl_i_18=0</TD>
    <TD>hstl_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_ii_18=0</TD>
    <TD>hsul_12=0</TD>
    <TD>lvcmos12=0</TD>
    <TD>lvcmos15=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvcmos18=0</TD>
    <TD>lvcmos25=0</TD>
    <TD>lvcmos33=1</TD>
    <TD>lvds_25=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvttl=0</TD>
    <TD>mini_lvds_25=0</TD>
    <TD>mobile_ddr=0</TD>
    <TD>pci33_3=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>ppds_25=0</TD>
    <TD>rsds_25=0</TD>
    <TD>sstl135=0</TD>
    <TD>sstl135_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl15=0</TD>
    <TD>sstl15_r=0</TD>
    <TD>sstl18_i=0</TD>
    <TD>sstl18_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>tmds_33=0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>memory</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>block_ram_tile_available=135</TD>
    <TD>block_ram_tile_fixed=0</TD>
    <TD>block_ram_tile_used=0</TD>
    <TD>block_ram_tile_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb18_available=270</TD>
    <TD>ramb18_fixed=0</TD>
    <TD>ramb18_used=0</TD>
    <TD>ramb18_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36_fifo_available=135</TD>
    <TD>ramb36_fifo_fixed=0</TD>
    <TD>ramb36_fifo_used=0</TD>
    <TD>ramb36_fifo_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>primitives</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufg_functional_category=Clock</TD>
    <TD>bufg_used=2</TD>
    <TD>carry4_functional_category=CarryLogic</TD>
    <TD>carry4_used=15</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdce_functional_category=Flop &amp; Latch</TD>
    <TD>fdce_used=60</TD>
    <TD>fdre_functional_category=Flop &amp; Latch</TD>
    <TD>fdre_used=2291</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibuf_functional_category=IO</TD>
    <TD>ibuf_used=2</TD>
    <TD>lut2_functional_category=LUT</TD>
    <TD>lut2_used=243</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut3_functional_category=LUT</TD>
    <TD>lut3_used=403</TD>
    <TD>lut4_functional_category=LUT</TD>
    <TD>lut4_used=665</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut5_functional_category=LUT</TD>
    <TD>lut5_used=710</TD>
    <TD>lut6_functional_category=LUT</TD>
    <TD>lut6_used=2111</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf7_functional_category=MuxFx</TD>
    <TD>muxf7_used=513</TD>
    <TD>muxf8_functional_category=MuxFx</TD>
    <TD>muxf8_used=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuf_functional_category=IO</TD>
    <TD>obuf_used=16</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>slice_logic</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>f7_muxes_available=31700</TD>
    <TD>f7_muxes_fixed=0</TD>
    <TD>f7_muxes_used=513</TD>
    <TD>f7_muxes_util_percentage=1.62</TD>
</TR><TR ALIGN='LEFT'>    <TD>f8_muxes_available=15850</TD>
    <TD>f8_muxes_fixed=0</TD>
    <TD>f8_muxes_used=8</TD>
    <TD>f8_muxes_util_percentage=0.05</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_available=63400</TD>
    <TD>lut_as_logic_fixed=0</TD>
    <TD>lut_as_logic_used=3377</TD>
    <TD>lut_as_logic_util_percentage=5.33</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_available=19000</TD>
    <TD>lut_as_memory_fixed=0</TD>
    <TD>lut_as_memory_used=0</TD>
    <TD>lut_as_memory_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_flip_flop_available=126800</TD>
    <TD>register_as_flip_flop_fixed=0</TD>
    <TD>register_as_flip_flop_used=2351</TD>
    <TD>register_as_flip_flop_util_percentage=1.85</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_latch_available=126800</TD>
    <TD>register_as_latch_fixed=0</TD>
    <TD>register_as_latch_used=0</TD>
    <TD>register_as_latch_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_luts_available=63400</TD>
    <TD>slice_luts_fixed=0</TD>
    <TD>slice_luts_used=3377</TD>
    <TD>slice_luts_util_percentage=5.33</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_available=126800</TD>
    <TD>slice_registers_fixed=0</TD>
    <TD>slice_registers_used=2351</TD>
    <TD>slice_registers_util_percentage=1.85</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=0</TD>
    <TD>lut_as_logic_available=63400</TD>
    <TD>lut_as_logic_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_used=3377</TD>
    <TD>lut_as_logic_util_percentage=5.33</TD>
    <TD>lut_as_memory_available=19000</TD>
    <TD>lut_as_memory_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_used=0</TD>
    <TD>lut_as_memory_util_percentage=0.00</TD>
    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_in_front_of_the_register_is_unused_fixed=0</TD>
    <TD>lut_in_front_of_the_register_is_unused_used=618</TD>
    <TD>lut_in_front_of_the_register_is_used_fixed=618</TD>
    <TD>lut_in_front_of_the_register_is_used_used=749</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_driven_from_outside_the_slice_fixed=749</TD>
    <TD>register_driven_from_outside_the_slice_used=1367</TD>
    <TD>register_driven_from_within_the_slice_fixed=1367</TD>
    <TD>register_driven_from_within_the_slice_used=984</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_available=15850</TD>
    <TD>slice_fixed=0</TD>
    <TD>slice_registers_available=126800</TD>
    <TD>slice_registers_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_used=2351</TD>
    <TD>slice_registers_util_percentage=1.85</TD>
    <TD>slice_used=1302</TD>
    <TD>slice_util_percentage=8.21</TD>
</TR><TR ALIGN='LEFT'>    <TD>slicel_fixed=0</TD>
    <TD>slicel_used=913</TD>
    <TD>slicem_fixed=0</TD>
    <TD>slicem_used=389</TD>
</TR><TR ALIGN='LEFT'>    <TD>unique_control_sets_available=15850</TD>
    <TD>unique_control_sets_fixed=15850</TD>
    <TD>unique_control_sets_used=260</TD>
    <TD>unique_control_sets_util_percentage=1.64</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o5_and_o6_fixed=1.64</TD>
    <TD>using_o5_and_o6_used=755</TD>
    <TD>using_o5_output_only_fixed=755</TD>
    <TD>using_o5_output_only_used=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o6_output_only_fixed=1</TD>
    <TD>using_o6_output_only_used=2621</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>specific_feature</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bscane2_available=4</TD>
    <TD>bscane2_fixed=0</TD>
    <TD>bscane2_used=0</TD>
    <TD>bscane2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>capturee2_available=1</TD>
    <TD>capturee2_fixed=0</TD>
    <TD>capturee2_used=0</TD>
    <TD>capturee2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>dna_port_available=1</TD>
    <TD>dna_port_fixed=0</TD>
    <TD>dna_port_used=0</TD>
    <TD>dna_port_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>efuse_usr_available=1</TD>
    <TD>efuse_usr_fixed=0</TD>
    <TD>efuse_usr_used=0</TD>
    <TD>efuse_usr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>frame_ecce2_available=1</TD>
    <TD>frame_ecce2_fixed=0</TD>
    <TD>frame_ecce2_used=0</TD>
    <TD>frame_ecce2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>icape2_available=2</TD>
    <TD>icape2_fixed=0</TD>
    <TD>icape2_used=0</TD>
    <TD>icape2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcie_2_1_available=1</TD>
    <TD>pcie_2_1_fixed=0</TD>
    <TD>pcie_2_1_used=0</TD>
    <TD>pcie_2_1_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>startupe2_available=1</TD>
    <TD>startupe2_fixed=0</TD>
    <TD>startupe2_used=0</TD>
    <TD>startupe2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>xadc_available=1</TD>
    <TD>xadc_fixed=0</TD>
    <TD>xadc_used=0</TD>
    <TD>xadc_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>synthesis</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-assert=default::[not_specified]</TD>
    <TD>-bufg=default::12</TD>
    <TD>-cascade_dsp=default::auto</TD>
    <TD>-constrset=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-control_set_opt_threshold=default::auto</TD>
    <TD>-directive=default::default</TD>
    <TD>-fanout_limit=default::10000</TD>
    <TD>-flatten_hierarchy=default::rebuilt</TD>
</TR><TR ALIGN='LEFT'>    <TD>-fsm_extraction=default::auto</TD>
    <TD>-gated_clock_conversion=default::off</TD>
    <TD>-generic=default::[not_specified]</TD>
    <TD>-include_dirs=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-keep_equivalent_registers=default::[not_specified]</TD>
    <TD>-max_bram=default::-1</TD>
    <TD>-max_bram_cascade_height=default::-1</TD>
    <TD>-max_dsp=default::-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-max_uram=default::-1</TD>
    <TD>-max_uram_cascade_height=default::-1</TD>
    <TD>-mode=default::default</TD>
    <TD>-name=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-no_lc=default::[not_specified]</TD>
    <TD>-no_srlextract=default::[not_specified]</TD>
    <TD>-no_timing_driven=default::[not_specified]</TD>
    <TD>-part=xc7a100tfgg484-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-resource_sharing=default::auto</TD>
    <TD>-retiming=default::[not_specified]</TD>
    <TD>-rtl=default::[not_specified]</TD>
    <TD>-rtl_skip_constraints=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-rtl_skip_ip=default::[not_specified]</TD>
    <TD>-seu_protect=default::none</TD>
    <TD>-sfcu=default::[not_specified]</TD>
    <TD>-shreg_min_size=default::3</TD>
</TR><TR ALIGN='LEFT'>    <TD>-top=SEQ_Display</TD>
    <TD>-verilog_define=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>elapsed=00:00:46s</TD>
    <TD>hls_ip=0</TD>
    <TD>memory_gain=781.125MB</TD>
    <TD>memory_peak=1101.469MB</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>xsim</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-sim_mode=behavioral</TD>
    <TD>-sim_type=default::</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
</BODY>
</HTML>
