module SEQ(
    input CLK,
    output [15:0] valE,
    output [1:0] Stat
);
    reg [15:0] PC;

    wire imem_error;
    wire [3:0] icode;
    wire [3:0] ifun;
    wire instr_valid;
    wire [3:0] rA;
    wire [3:0] rB;
    wire [15:0] valC;
    wire [15:0] valP;
    wire [3:0] srcA;
    wire [3:0] srcB;
    wire [15:0] valA;
    wire [15:0] valB;
    wire Cnd;
    wire [3:0] dstE;
    wire [3:0] dstM;
    wire [15:0] valM;
    //wire [1:0] Stat;
    wire [15:0] new_PC;
    
    initial begin
        PC = 16'h0;
    end
    
    fetch u_f(
        .PC             (PC             ),
        .icode          (icode          ),
        .ifun           (ifun           ),
        .rA             (rA             ),
        .rB             (rB             ),
        .valC           (valC           ),
        .valP           (valP           ),
        .imem_error     (imem_error     ),
        .instr_valid    (instr_valid    )
    );
    
    reg_file u_rf(
        .CLK    (CLK    ),
        .dstE   (dstE   ),
        .dstM   (dstM   ),
        .srcA   (srcA   ),
        .srcB   (srcB   ),
        .valE   (valE   ),
        .valM   (valM   ),
        .valA   (valA   ),
        .valB   (valB   )    
    );   
    
    decode u_d(
        .icode  (icode  ),
        .rA     (rA     ),
        .rB     (rB     ),
        .srcA   (srcA   ),
        .srcB   (srcB   )
    );
    
    execute u_e(
        .CLK    (CLK    ),
        .icode  (icode  ),
        .ifun   (ifun   ),
        .valA   (valA   ),
        .valB   (valB   ),
        .valC   (valC   ),
        .valE   (valE   ),
        .Cnd    (Cnd    )
    );
    
    memory u_m(
        .CLK            (CLK            ),
        .icode          (icode          ),
        .valA           (valA           ),
        .valE           (valE           ),
        .valP           (valP           ),
        .imem_error     (imem_error     ),
        .instr_valid    (instr_valid    ),
        .valM           (valM           ),
        .Stat           (Stat           )
    );
                        
    write_back u_w(
        .icode  (icode  ),
        .rA     (rA     ),
        .rB     (rB     ),
        .Cnd    (Cnd    ),
        .dstE   (dstE   ),
        .dstM   (dstM   )
    );
    
    update u_u(
        .icode  (icode  ),
        .Cnd    (Cnd    ),
        .valC   (valC   ),
        .valM   (valM   ),
        .valP   (valP   ),
        .new_PC (new_PC )
    );
    
    always @ (posedge CLK) begin
        if (Stat == 2'h0) begin
            PC <= new_PC;
        end
    end
    
endmodule

module fetch(
    input       [15:0]  PC              ,
    output      [3:0]   icode           ,
    output      [3:0]   ifun            ,
    output      [3:0]   rA              ,
    output      [3:0]   rB              ,
    output  reg [15:0]  valC            ,
    output      [15:0]  valP            ,
    output              imem_error      ,
    output  reg         instr_valid     
);
    reg [7:0] M [0:255];
    
    wire need_valC;
    wire need_regids;
    
    integer i;
    initial begin
        // .pos 0
        M[16'h000] = 8'h30;    // irmovw
        M[16'h001] = 8'hf4;    // %sp
        M[16'h002] = 8'h80;
        M[16'h003] = 8'h00;    // stack
        
        M[16'h004] = 8'h80;    // call
        M[16'h005] = 8'h08;
        M[16'h006] = 8'h00;    // main
        
        M[16'h007] = 8'h00;    // halt
        
        // main:
        M[16'h008] = 8'h30;    // irmovw
        M[16'h009] = 8'hf7;    // %di
        M[16'h00a] = 8'h00;
        M[16'h00b] = 8'h00;    // array
        
        M[16'h00c] = 8'h30;    // irmovw
        M[16'h00d] = 8'hf6;    // %si
        M[16'h00e] = 8'h04;
        M[16'h00f] = 8'h00;    // $4
        
        M[16'h010] = 8'h80;    // call
        M[16'h011] = 8'h14;
        M[16'h012] = 8'h00;    // sum
        
        M[16'h013] = 8'h90;    // ret
        
        // sum:
        M[16'h014] = 8'h30;    // irmovw
        M[16'h015] = 8'hf8;    // %r8w
        M[16'h016] = 8'h02;
        M[16'h017] = 8'h00;    // $2
        
        M[16'h018] = 8'h30;    // irmovw
        M[16'h019] = 8'hf9;    // %r9w
        M[16'h01a] = 8'h01;
        M[16'h01b] = 8'h00;    // $1
        
        M[16'h01c] = 8'h63;    // xorw
        M[16'h01d] = 8'h00;    // %ax,%ax
        
        M[16'h01e] = 8'h62;    // andw
        M[16'h01f] = 8'h66;    // %si,%si
        
        M[16'h020] = 8'h70;    // jmp
        M[16'h021] = 8'h2d;
        M[16'h022] = 8'h00;    // test
        
        // loop:
        M[16'h023] = 8'h50;    // mrmovw
        M[16'h024] = 8'ha7;    // (%di),%r10w
        M[16'h025] = 8'h00;
        M[16'h026] = 8'h00;    // $0
        
        M[16'h027] = 8'h60;    // addw
        M[16'h028] = 8'ha0;    // %r10w,%ax
        
        M[16'h029] = 8'h60;    // addw
        M[16'h02a] = 8'h87;    // %r8w,%di
        
        M[16'h02b] = 8'h61;    // subw
        M[16'h02c] = 8'h96;    // %r9w,%si
        
        // test:
        M[16'h02d] = 8'h74;    // jne
        M[16'h02e] = 8'h23;
        M[16'h02f] = 8'h00;    // loop
        
        M[16'h030] = 8'h90;    // ret
        
        for (i = 16'h031; i < 16'h100; i = i + 1) begin
            M[i] = 8'h00;
        end
    end
    
    assign imem_error = (PC >= 16'h100);
    
    assign { icode, ifun } = imem_error ? { 4'h0, 4'h0 } : M[PC];
    
    always @ (*) begin    // instr_valid
        if (imem_error) begin
            instr_valid = 0;
        end
        else if (icode > 4'hB) begin    // invalid icode
            instr_valid = 0;
        end
        else if (icode == 4'h2 && ifun > 4'h6) begin    // invalid cmovXX
            instr_valid = 0;
        end
        else if (icode == 4'h6 && ifun > 4'h3) begin    // invalid OPw
            instr_valid = 0;
        end
        else if (icode == 4'h7 && ifun > 4'h6) begin    // invalid jXX
            instr_valid = 0;
        end
        else begin
            instr_valid = 1;
        end
    end

    assign need_valC = (icode == 4'h3 || icode == 4'h4 || icode == 4'h5 || icode == 4'h7 || icode == 4'h8);
    assign need_regids = ((icode > 4'h1 && icode < 4'h7) || icode == 4'hA || icode == 4'hB);
    
    
    assign { rA, rB } = need_regids ?  M[PC + 1] : { 4'hF, 4'hF };
    
    always @ (*) begin    // valC
        if (need_valC) begin
            if (need_regids) begin
                valC = { M[PC + 3], M[PC + 2] };
            end
            else begin
                valC = { M[PC + 2], M[PC + 1] };
            end
        end
        else begin
            valC = 16'h0;
        end
    end

    assign valP = PC + 1 + (need_valC ? 2 : 0) + (need_regids ? 1 : 0);

endmodule

module reg_file(
    input           CLK     ,
    input   [3:0]   dstE    ,
    input   [3:0]   dstM    ,
    input   [3:0]   srcA    ,
    input   [3:0]   srcB    ,
    input   [15:0]  valE    ,
    input   [15:0]  valM    ,
    output  [15:0]  valA    ,
    output  [15:0]  valB    
);                 
    reg [15:0] R [0:14];
    
    integer i = 0;
    initial begin
        for (i = 0; i < 15; i = i + 1) begin
            R[i] = 16'h0;
        end
    end
    
    assign valA = (srcA == 4'hF) ? 16'h0 : R[srcA];
    assign valB = (srcB == 4'hF) ? 16'h0 : R[srcB];
    
    always @ (posedge CLK) begin
        if (dstE != 4'hF) begin
            R[dstE] <= valE;
        end
        if (dstM != 4'hF) begin
            R[dstM] <= valM;
        end
    end

endmodule

module decode(
    input       [3:0]   icode   ,
    input       [3:0]   rA      ,
    input       [3:0]   rB      ,
    output  reg [3:0]   srcA    ,
    output  reg [3:0]   srcB    
);
    always @ (*) begin
        if (icode == 4'h2 || icode == 4'h4 || icode == 4'h6 || icode == 4'hA) begin    // rrmovw, rrmovw, OPw, pushw
            srcA = rA;
        end
        else if (icode == 4'h9 || icode == 4'hB) begin    // ret, popw
            srcA = 4'h4;    // %sp
        end
        else begin    // else
            srcA = 4'hF;
        end
    end
    
    always @ (*) begin
        if (icode == 4'h4 || icode == 4'h5 || icode == 4'h6) begin    // rmmovw, mrmovw, OPw
            srcB = rB;
        end
        else if (icode == 4'h8 || icode == 4'h9 || icode == 4'hA || icode == 4'hB) begin    // call, ret, pushw, popw
            srcB = 4'h4;    // %sp
        end
        else begin    // else
            srcB = 4'hF;
        end
    end

endmodule

module execute(
    input               CLK     ,
    input       [3:0]   icode   ,
    input       [3:0]   ifun    ,
    input       [15:0]  valA    ,
    input       [15:0]  valB    ,
    input       [15:0]  valC    ,
    output  reg [15:0]  valE    ,
    output  reg         Cnd        
);

    reg [15:0] aluA;
    reg [15:0] aluB;
    reg [3:0] alufun;
    reg new_ZF;
    reg new_SF;
    reg new_OF;
    wire setCC;
    reg ZF;
    reg SF;
    reg OF;
    
    initial begin
        ZF = 0;
        SF = 0;
        OF = 0;
    end
    
    always @ (*) begin    // ALU A
        if (icode == 4'h2 || icode == 4'h6) begin    // rrmovw, OPw
            aluA = valA;
        end
        else if (icode == 4'h3 || icode == 4'h4 || icode == 4'h5) begin    // irmovw, rmmovw, mrmovw
            aluA = valC;
        end
        else if (icode == 4'h8 || icode == 4'hA) begin    // call, pushw
            aluA = -16'h8;
        end
        else if (icode == 4'h9 || icode == 4'hB) begin    // ret, popw, mrmovw
            aluA = 16'h8;
        end
        else begin    // else
            aluA = 16'h0;
        end
    end
    
    always @ (*) begin    // ALU B
        if (icode == 4'h2 || icode == 4'h3) begin    // rrmovw, irmovw
            aluB = 16'h0;
        end
        else if (icode == 4'h7) begin    // jXX
            aluB = 16'h0;
        end
        else begin    // else
            aluB = valB;
        end
    end
    
    
    always @ (*) begin    // ALU fun
        if (icode == 4'h6) begin    // OPw
            alufun = ifun;
        end
        else begin    // else
            alufun = 4'h0;
        end
    end
    

    always @ (*) begin    // ALU
        case (alufun)
            4'h0 : valE = aluB + aluA;    // addw
            4'h1 : valE = aluB - aluA;    // subw
            4'h2 : valE = aluB & aluA;    // andw
            4'h3 : valE = aluB ^ aluA;    // xorw
            default : valE = 16'h0;
        endcase
        
        new_ZF = ~|valE;    // valE == 0
        new_SF = valE[15];    // valE < 0
        if (alufun == 4'h0 && aluA[15] == aluB[15] && aluA[15] ^ valE[15]) begin    // addw overflow
            new_OF = 1;
        end
        else if (alufun == 4'h1 && aluA[15] ^ aluB[15] && aluA[15] == valE[15]) begin    // subw overflow
            new_OF = 1;
        end
        else begin
            new_OF = 0;
        end
    end
    
    assign setCC = (icode == 4'h6);    // Set CC
    
    always @ (posedge CLK) begin    // CC
        if (setCC) begin
            ZF <= new_ZF;
            SF <= new_SF;
            OF <= new_OF;
        end
    end
    
    always @ (*)
    begin    // cond
        case (ifun)
            4'h0 : Cnd = 1;
            4'h1 : Cnd = (SF ^ OF) | ZF;
            4'h2 : Cnd = SF ^ OF;
            4'h3 : Cnd = ZF;
            4'h4 : Cnd = ~ZF;
            4'h5 : Cnd = ~(SF ^ OF);
            4'h6 : Cnd = ~(SF ^ OF) & ~ZF;
            default : Cnd = 1;
        endcase
    end
    
endmodule

module memory(
    input               CLK         ,
    input       [3:0]   icode       ,
    input       [15:0]  valA        ,
    input       [15:0]  valE        ,
    input       [15:0]  valP        ,
    input               imem_error  ,
    input               instr_valid ,
    output      [15:0]  valM        ,
    output  reg [1:0]   Stat
);
    reg [7:0] M [0:255];
    wire mem_read;
    wire mem_write;
    wire [15:0] mem_addr;
    wire [15:0] mem_data;
    wire dmem_error;
    
    integer i;
    initial begin
        // array:
        M[16'h000] = 8'h0d;
        M[16'h001] = 8'h00;
        M[16'h002] = 8'hc0;
        M[16'h003] = 8'h00;
        M[16'h004] = 8'h00;
        M[16'h005] = 8'h0b;
        M[16'h006] = 8'h00;
        M[16'h007] = 8'ha0;
        for (i = 16'h08; i < 16'h100; i = i + 1) begin
            M[i] = 8'h00;
        end
    end
    
    assign mem_read = (icode == 4'h5 || icode == 4'h9 || icode == 4'hB);
    assign mem_write = (icode == 4'h4 || icode == 4'h8 || icode == 4'hA);
    assign mem_addr = (icode == 4'h9 || icode == 4'hB) ? valA : valE;
    assign mem_data = (icode == 4'h8) ? valP : valA;
    
    assign dmem_error = ((mem_read || mem_write) && mem_addr >= 16'h100);
    
    always @ (posedge CLK) begin
        if (mem_write && !dmem_error) begin
            M[mem_addr] <= mem_data[7:0];
            M[mem_addr + 1] <= mem_data[15:8];
        end
    end
    
    assign valM = (mem_read && !dmem_error) ? { M[mem_addr + 1], M[mem_addr] } : 16'h0;
    
    always @ (*) begin
        if (imem_error || dmem_error) begin
            Stat = 2;    // ADR
        end
        else if (instr_valid == 0) begin
            Stat = 3;    // INS
        end
        else if (icode == 0) begin
            Stat = 1;    // HLT
        end
        else begin
            Stat = 0;    // AOK
        end
    end

endmodule

module write_back(
    input       [3:0]   icode   ,
    input       [3:0]   rA      ,
    input       [3:0]   rB      ,
    input               Cnd     ,
    output  reg [3:0]   dstE    ,
    output  reg [3:0]   dstM    
);
    always @ (*) begin
        if (icode == 4'h2 && Cnd || icode == 4'h3 || icode == 4'h6) begin    // rrmovw, cmovXX, irmovw, OPw
            dstE = rB;
        end
        else if (icode == 4'h8 || icode == 4'h9 || icode == 4'hA || icode == 4'hB) begin    // call, ret, pushw, popw
            dstE = 4'h4;    // %sp
        end
        else begin    // else
            dstE = 4'hF;
        end
    end
    
    always @ (*) begin
        if (icode == 4'h5 || icode == 4'hB) begin    // mrmovw, popw
            dstM = rA;
        end
        else begin    // else
            dstM = 4'hF;
        end
    end

endmodule

module update(
    input       [3:0]   icode   ,
    input               Cnd     ,
    input       [15:0]  valC    ,
    input       [15:0]  valM    ,
    input       [15:0]  valP    ,
    output  reg [15:0]  new_PC  
);
    always @ (*) begin
        if (icode == 4'h7 && Cnd) begin    // jxx
            new_PC = valC;
        end
        else if (icode == 4'h8) begin    // call
            new_PC = valC;
        end
        else if (icode == 4'h9) begin    // ret
            new_PC = valM;
        end
        else begin                        // else
            new_PC = valP;
        end
    end

endmodule
