#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Wed Jun  4 00:22:40 2025
# Process ID: 30712
# Current directory: E:/YS/Y86_3/Y86_3.runs/synth_1
# Command line: vivado.exe -log SEQ_Display.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source SEQ_Display.tcl
# Log file: E:/YS/Y86_3/Y86_3.runs/synth_1/SEQ_Display.vds
# Journal file: E:/YS/Y86_3/Y86_3.runs/synth_1\vivado.jou
#-----------------------------------------------------------
source SEQ_Display.tcl -notrace
Command: synth_design -top SEQ_Display -part xc7a100tfgg484-1
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a100t'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 3020 
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 429.992 ; gain = 98.137
---------------------------------------------------------------------------------
INFO: [Synth 8-6157] synthesizing module 'SEQ_Display' [E:/YS/Y86_3/src/SEQ16.v:24]
INFO: [Synth 8-6157] synthesizing module 'clock_divider' [E:/YS/Y86_3/src/SEQ16.v:2]
INFO: [Synth 8-6155] done synthesizing module 'clock_divider' (1#1) [E:/YS/Y86_3/src/SEQ16.v:2]
INFO: [Synth 8-6157] synthesizing module 'SEQ' [E:/YS/Y86_3/src/SEQ16.v:75]
INFO: [Synth 8-6157] synthesizing module 'fetch' [E:/YS/Y86_3/src/SEQ16.v:196]
INFO: [Synth 8-6155] done synthesizing module 'fetch' (2#1) [E:/YS/Y86_3/src/SEQ16.v:196]
INFO: [Synth 8-6157] synthesizing module 'reg_file' [E:/YS/Y86_3/src/SEQ16.v:340]
INFO: [Synth 8-6085] Hierarchical reference on 'R' stops possible memory inference [E:/YS/Y86_3/src/SEQ16.v:351]
INFO: [Synth 8-6155] done synthesizing module 'reg_file' (3#1) [E:/YS/Y86_3/src/SEQ16.v:340]
INFO: [Synth 8-6157] synthesizing module 'decode' [E:/YS/Y86_3/src/SEQ16.v:374]
INFO: [Synth 8-6155] done synthesizing module 'decode' (4#1) [E:/YS/Y86_3/src/SEQ16.v:374]
INFO: [Synth 8-6157] synthesizing module 'execute' [E:/YS/Y86_3/src/SEQ16.v:407]
INFO: [Synth 8-6155] done synthesizing module 'execute' (5#1) [E:/YS/Y86_3/src/SEQ16.v:407]
INFO: [Synth 8-6157] synthesizing module 'memory' [E:/YS/Y86_3/src/SEQ16.v:524]
WARNING: [Synth 8-4767] Trying to implement RAM 'M_reg' in registers. Block RAM or DRAM implementation is not possible; see log for reasons.
Reason is one or more of the following :
	1: RAM has multiple writes via different ports in same process. If RAM inferencing intended, write to one port per process. 
	2: Unable to determine number of words or word size in RAM. 
	3: No valid read/write found for RAM. 
RAM "M_reg" dissolved into registers
INFO: [Synth 8-6155] done synthesizing module 'memory' (6#1) [E:/YS/Y86_3/src/SEQ16.v:524]
INFO: [Synth 8-6157] synthesizing module 'write_back' [E:/YS/Y86_3/src/SEQ16.v:591]
INFO: [Synth 8-6155] done synthesizing module 'write_back' (7#1) [E:/YS/Y86_3/src/SEQ16.v:591]
INFO: [Synth 8-6157] synthesizing module 'update' [E:/YS/Y86_3/src/SEQ16.v:622]
INFO: [Synth 8-6155] done synthesizing module 'update' (8#1) [E:/YS/Y86_3/src/SEQ16.v:622]
INFO: [Synth 8-6155] done synthesizing module 'SEQ' (9#1) [E:/YS/Y86_3/src/SEQ16.v:75]
INFO: [Synth 8-6085] Hierarchical reference on 'R' stops possible memory inference [E:/YS/Y86_3/src/SEQ16.v:351]
INFO: [Synth 8-6155] done synthesizing module 'SEQ_Display' (10#1) [E:/YS/Y86_3/src/SEQ16.v:24]
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 511.008 ; gain = 179.152
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:02 ; elapsed = 00:00:03 . Memory (MB): peak = 511.008 ; gain = 179.152
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:02 ; elapsed = 00:00:03 . Memory (MB): peak = 511.008 ; gain = 179.152
---------------------------------------------------------------------------------
INFO: [Device 21-403] Loading part xc7a100tfgg484-1
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [E:/YS/Y86_3/src/constraints.xdc]
Finished Parsing XDC File [E:/YS/Y86_3/src/constraints.xdc]
INFO: [Project 1-236] Implementation specific constraints were found while reading constraint file [E:/YS/Y86_3/src/constraints.xdc]. These constraints will be ignored for synthesis but will be used in implementation. Impacted constraints are listed in the file [.Xil/SEQ_Display_propImpl.xdc].
Resolution: To avoid this warning, move constraints listed in [.Xil/SEQ_Display_propImpl.xdc] to another XDC file and exclude this new file from synthesis with the used_in_synthesis property (File Properties dialog in GUI) and re-run elaboration/synthesis.
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 864.371 ; gain = 0.000
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 864.371 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 864.371 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 864.371 ; gain = 0.000
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:07 ; elapsed = 00:00:08 . Memory (MB): peak = 864.371 ; gain = 532.516
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a100tfgg484-1
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:07 ; elapsed = 00:00:08 . Memory (MB): peak = 864.371 ; gain = 532.516
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:07 ; elapsed = 00:00:08 . Memory (MB): peak = 864.371 ; gain = 532.516
---------------------------------------------------------------------------------
INFO: [Synth 8-5544] ROM "valP1" won't be mapped to Block RAM because address size (1) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "valP0" won't be mapped to Block RAM because address size (1) smaller than threshold (5)
INFO: [Synth 8-5546] ROM "M" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "M" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "M" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "M" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5818] HDL ADVISOR - The operator resource <adder> is shared. To prevent sharing consider applying a KEEP on the output of the operator [E:/YS/Y86_3/src/SEQ16.v:477]
INFO: [Synth 8-5544] ROM "Stat" won't be mapped to Block RAM because address size (4) smaller than threshold (5)
INFO: [Synth 8-5544] ROM "PC" won't be mapped to Block RAM because address size (2) smaller than threshold (5)
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:10 ; elapsed = 00:00:13 . Memory (MB): peak = 864.371 ; gain = 532.516
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
+---Adders : 
	   4 Input     16 Bit       Adders := 1     
	   3 Input     16 Bit       Adders := 1     
	   2 Input      8 Bit       Adders := 3     
+---XORs : 
	   2 Input     16 Bit         XORs := 1     
	   2 Input      1 Bit         XORs := 3     
+---Registers : 
	               16 Bit    Registers := 17    
	                8 Bit    Registers := 256   
	                1 Bit    Registers := 5     
+---Muxes : 
	   2 Input     16 Bit        Muxes := 72    
	   5 Input     16 Bit        Muxes := 1     
	   4 Input     16 Bit        Muxes := 2     
	 257 Input      8 Bit        Muxes := 4     
	   2 Input      8 Bit        Muxes := 9     
	   3 Input      4 Bit        Muxes := 3     
	   2 Input      4 Bit        Muxes := 2     
	   2 Input      2 Bit        Muxes := 2     
	   2 Input      1 Bit        Muxes := 385   
	   3 Input      1 Bit        Muxes := 128   
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
Module SEQ_Display 
Detailed RTL Component Info : 
+---Registers : 
	               16 Bit    Registers := 1     
	                1 Bit    Registers := 1     
+---Muxes : 
	   2 Input     16 Bit        Muxes := 1     
Module clock_divider 
Detailed RTL Component Info : 
+---Registers : 
	                1 Bit    Registers := 1     
Module fetch 
Detailed RTL Component Info : 
+---Adders : 
	   4 Input     16 Bit       Adders := 1     
	   2 Input      8 Bit       Adders := 3     
+---Muxes : 
	   2 Input     16 Bit        Muxes := 2     
	 257 Input      8 Bit        Muxes := 4     
	   2 Input      8 Bit        Muxes := 1     
Module reg_file 
Detailed RTL Component Info : 
+---Registers : 
	               16 Bit    Registers := 15    
+---Muxes : 
	   2 Input     16 Bit        Muxes := 60    
Module decode 
Detailed RTL Component Info : 
+---Muxes : 
	   3 Input      4 Bit        Muxes := 2     
Module execute 
Detailed RTL Component Info : 
+---Adders : 
	   3 Input     16 Bit       Adders := 1     
+---XORs : 
	   2 Input     16 Bit         XORs := 1     
	   2 Input      1 Bit         XORs := 3     
+---Registers : 
	                1 Bit    Registers := 3     
+---Muxes : 
	   5 Input     16 Bit        Muxes := 1     
	   2 Input     16 Bit        Muxes := 3     
	   4 Input     16 Bit        Muxes := 2     
	   2 Input      4 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 1     
Module memory 
Detailed RTL Component Info : 
+---Registers : 
	                8 Bit    Registers := 256   
+---Muxes : 
	   2 Input     16 Bit        Muxes := 3     
	   2 Input      8 Bit        Muxes := 8     
	   2 Input      2 Bit        Muxes := 2     
	   2 Input      1 Bit        Muxes := 383   
	   3 Input      1 Bit        Muxes := 128   
Module write_back 
Detailed RTL Component Info : 
+---Muxes : 
	   3 Input      4 Bit        Muxes := 1     
	   2 Input      4 Bit        Muxes := 1     
Module update 
Detailed RTL Component Info : 
+---Muxes : 
	   2 Input     16 Bit        Muxes := 3     
Module SEQ 
Detailed RTL Component Info : 
+---Registers : 
	               16 Bit    Registers := 1     
+---Muxes : 
	   2 Input      1 Bit        Muxes := 1     
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 240 (col length:80)
BRAMs: 270 (col length: RAMB18 80 RAMB36 40)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
INFO: [Synth 8-5546] ROM "p_0_out" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "p_0_out" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "p_0_out" won't be mapped to RAM because it is too sparse
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:32 ; elapsed = 00:00:36 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:36 ; elapsed = 00:00:41 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:37 ; elapsed = 00:00:42 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:39 ; elapsed = 00:00:44 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:40 ; elapsed = 00:00:45 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:40 ; elapsed = 00:00:45 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:40 ; elapsed = 00:00:45 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:40 ; elapsed = 00:00:45 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:40 ; elapsed = 00:00:45 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:40 ; elapsed = 00:00:45 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+-+--------------+----------+
| |BlackBox name |Instances |
+-+--------------+----------+
+-+--------------+----------+

Report Cell Usage: 
+------+-------+------+
|      |Cell   |Count |
+------+-------+------+
|1     |BUFG   |     2|
|2     |CARRY4 |    15|
|3     |LUT1   |     3|
|4     |LUT2   |   243|
|5     |LUT3   |   403|
|6     |LUT4   |   665|
|7     |LUT5   |   710|
|8     |LUT6   |  2111|
|9     |MUXF7  |   513|
|10    |MUXF8  |     8|
|11    |FDCE   |    60|
|12    |FDRE   |  2291|
|13    |IBUF   |     2|
|14    |OBUF   |    16|
+------+-------+------+

Report Instance Areas: 
+------+------------+--------------+------+
|      |Instance    |Module        |Cells |
+------+------------+--------------+------+
|1     |top         |              |  7042|
|2     |  clk_div   |clock_divider |    65|
|3     |  processor |SEQ           |  6940|
|4     |    u_e     |execute       |   341|
|5     |    u_f     |fetch         |     2|
|6     |    u_m     |memory        |  3792|
|7     |    u_rf    |reg_file      |  2770|
+------+------------+--------------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:40 ; elapsed = 00:00:45 . Memory (MB): peak = 1101.469 ; gain = 769.613
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 0 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:34 ; elapsed = 00:00:41 . Memory (MB): peak = 1101.469 ; gain = 416.250
Synthesis Optimization Complete : Time (s): cpu = 00:00:40 ; elapsed = 00:00:45 . Memory (MB): peak = 1101.469 ; gain = 769.613
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Netlist 29-17] Analyzing 536 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1101.469 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

INFO: [Common 17-83] Releasing license: Synthesis
46 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:42 ; elapsed = 00:00:48 . Memory (MB): peak = 1101.469 ; gain = 782.660
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1101.469 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'E:/YS/Y86_3/Y86_3.runs/synth_1/SEQ_Display.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file SEQ_Display_utilization_synth.rpt -pb SEQ_Display_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Wed Jun  4 00:23:30 2025...
