<?xml version="1.0" encoding="UTF-8"?>
<GenRun Id="impl_1" LaunchPart="xc7a100tfgg484-1" LaunchTime="1748968328">
  <File Type="OPT-HWDEF" Name="SEQ_Display.hwdef"/>
  <File Type="ROUTE-BUS-SKEW" Name="SEQ_Display_bus_skew_routed.rpt"/>
  <File Type="PA-DCP" Name="SEQ_Display.dcp"/>
  <File Type="BITSTR-BMM" Name="SEQ_Display_bd.bmm"/>
  <File Type="OPT-METHODOLOGY-DRC" Name="SEQ_Display_methodology_drc_opted.rpt"/>
  <File Type="INIT-TIMING" Name="SEQ_Display_timing_summary_init.rpt"/>
  <File Type="ROUTE-PWR" Name="SEQ_Display_power_routed.rpt"/>
  <File Type="PA-TCL" Name="SEQ_Display.tcl"/>
  <File Type="OPT-TIMING" Name="SEQ_Display_timing_summary_opted.rpt"/>
  <File Type="OPT-DCP" Name="SEQ_Display_opt.dcp"/>
  <File Type="ROUTE-PWR-SUM" Name="SEQ_Display_power_summary_routed.pb"/>
  <File Type="REPORTS-TCL" Name="SEQ_Display_reports.tcl"/>
  <File Type="OPT-DRC" Name="SEQ_Display_drc_opted.rpt"/>
  <File Type="PWROPT-DCP" Name="SEQ_Display_pwropt.dcp"/>
  <File Type="PWROPT-DRC" Name="SEQ_Display_drc_pwropted.rpt"/>
  <File Type="PWROPT-TIMING" Name="SEQ_Display_timing_summary_pwropted.rpt"/>
  <File Type="PLACE-DCP" Name="SEQ_Display_placed.dcp"/>
  <File Type="PLACE-IO" Name="SEQ_Display_io_placed.rpt"/>
  <File Type="PLACE-CLK" Name="SEQ_Display_clock_utilization_placed.rpt"/>
  <File Type="PLACE-UTIL" Name="SEQ_Display_utilization_placed.rpt"/>
  <File Type="PLACE-UTIL-PB" Name="SEQ_Display_utilization_placed.pb"/>
  <File Type="PLACE-CTRL" Name="SEQ_Display_control_sets_placed.rpt"/>
  <File Type="PLACE-SIMILARITY" Name="SEQ_Display_incremental_reuse_placed.rpt"/>
  <File Type="PLACE-PRE-SIMILARITY" Name="SEQ_Display_incremental_reuse_pre_placed.rpt"/>
  <File Type="PLACE-TIMING" Name="SEQ_Display_timing_summary_placed.rpt"/>
  <File Type="POSTPLACE-PWROPT-DCP" Name="SEQ_Display_postplace_pwropt.dcp"/>
  <File Type="BG-BIN" Name="SEQ_Display.bin"/>
  <File Type="POSTPLACE-PWROPT-TIMING" Name="SEQ_Display_timing_summary_postplace_pwropted.rpt"/>
  <File Type="PHYSOPT-DCP" Name="SEQ_Display_physopt.dcp"/>
  <File Type="PHYSOPT-DRC" Name="SEQ_Display_drc_physopted.rpt"/>
  <File Type="BITSTR-MSK" Name="SEQ_Display.msk"/>
  <File Type="PHYSOPT-TIMING" Name="SEQ_Display_timing_summary_physopted.rpt"/>
  <File Type="ROUTE-ERROR-DCP" Name="SEQ_Display_routed_error.dcp"/>
  <File Type="ROUTE-DCP" Name="SEQ_Display_routed.dcp"/>
  <File Type="ROUTE-BLACKBOX-DCP" Name="SEQ_Display_routed_bb.dcp"/>
  <File Type="ROUTE-DRC" Name="SEQ_Display_drc_routed.rpt"/>
  <File Type="ROUTE-DRC-PB" Name="SEQ_Display_drc_routed.pb"/>
  <File Type="BITSTR-LTX" Name="debug_nets.ltx"/>
  <File Type="BITSTR-LTX" Name="SEQ_Display.ltx"/>
  <File Type="ROUTE-DRC-RPX" Name="SEQ_Display_drc_routed.rpx"/>
  <File Type="BITSTR-MMI" Name="SEQ_Display.mmi"/>
  <File Type="ROUTE-METHODOLOGY-DRC" Name="SEQ_Display_methodology_drc_routed.rpt"/>
  <File Type="ROUTE-METHODOLOGY-DRC-RPX" Name="SEQ_Display_methodology_drc_routed.rpx"/>
  <File Type="BITSTR-SYSDEF" Name="SEQ_Display.sysdef"/>
  <File Type="ROUTE-METHODOLOGY-DRC-PB" Name="SEQ_Display_methodology_drc_routed.pb"/>
  <File Type="ROUTE-PWR-RPX" Name="SEQ_Display_power_routed.rpx"/>
  <File Type="ROUTE-STATUS" Name="SEQ_Display_route_status.rpt"/>
  <File Type="ROUTE-STATUS-PB" Name="SEQ_Display_route_status.pb"/>
  <File Type="ROUTE-TIMINGSUMMARY" Name="SEQ_Display_timing_summary_routed.rpt"/>
  <File Type="ROUTE-TIMING-PB" Name="SEQ_Display_timing_summary_routed.pb"/>
  <File Type="ROUTE-TIMING-RPX" Name="SEQ_Display_timing_summary_routed.rpx"/>
  <File Type="ROUTE-SIMILARITY" Name="SEQ_Display_incremental_reuse_routed.rpt"/>
  <File Type="ROUTE-CLK" Name="SEQ_Display_clock_utilization_routed.rpt"/>
  <File Type="ROUTE-BUS-SKEW-PB" Name="SEQ_Display_bus_skew_routed.pb"/>
  <File Type="ROUTE-BUS-SKEW-RPX" Name="SEQ_Display_bus_skew_routed.rpx"/>
  <File Type="POSTROUTE-PHYSOPT-DCP" Name="SEQ_Display_postroute_physopt.dcp"/>
  <File Type="POSTROUTE-PHYSOPT-BLACKBOX-DCP" Name="SEQ_Display_postroute_physopt_bb.dcp"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING" Name="SEQ_Display_timing_summary_postroute_physopted.rpt"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING-PB" Name="SEQ_Display_timing_summary_postroute_physopted.pb"/>
  <File Type="POSTROUTE-PHYSOPT-TIMING-RPX" Name="SEQ_Display_timing_summary_postroute_physopted.rpx"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW" Name="SEQ_Display_bus_skew_postroute_physopted.rpt"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW-PB" Name="SEQ_Display_bus_skew_postroute_physopted.pb"/>
  <File Type="BG-BIT" Name="SEQ_Display.bit"/>
  <File Type="POSTROUTE-PHYSOPT-BUS-SKEW-RPX" Name="SEQ_Display_bus_skew_postroute_physopted.rpx"/>
  <File Type="BITSTR-RBT" Name="SEQ_Display.rbt"/>
  <File Type="BITSTR-NKY" Name="SEQ_Display.nky"/>
  <File Type="BG-BGN" Name="SEQ_Display.bgn"/>
  <File Type="BG-DRC" Name="SEQ_Display.drc"/>
  <File Type="RDI-RDI" Name="SEQ_Display.vdi"/>
  <File Type="WBT-USG" Name="usage_statistics_webtalk.html"/>
  <FileSet Name="sources" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1">
    <Filter Type="Srcs"/>
    <File Path="$PPRDIR/src/SEQ16.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="DesignMode" Val="RTL"/>
      <Option Name="TopModule" Val="SEQ_Display"/>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <FileSet Name="constrs_in" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1">
    <Filter Type="Constrs"/>
    <File Path="$PPRDIR/src/constraints.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="TargetConstrsFile" Val="$PPRDIR/src/constraints.xdc"/>
      <Option Name="ConstrsType" Val="XDC"/>
    </Config>
  </FileSet>
  <FileSet Name="utils" Type="Utils" RelSrcDir="$PSRCDIR/utils_1">
    <Filter Type="Utils"/>
    <Config>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <Strategy Version="1" Minor="2">
    <StratHandle Name="Vivado Implementation Defaults" Flow="Vivado Implementation 2018"/>
    <Step Id="init_design"/>
    <Step Id="opt_design"/>
    <Step Id="power_opt_design"/>
    <Step Id="place_design"/>
    <Step Id="post_place_power_opt_design"/>
    <Step Id="phys_opt_design"/>
    <Step Id="route_design"/>
    <Step Id="post_route_phys_opt_design"/>
    <Step Id="write_bitstream"/>
  </Strategy>
</GenRun>
