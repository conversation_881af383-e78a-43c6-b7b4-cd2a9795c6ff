`timescale 1ns / 1ps

module testbench();
    reg CLK;
    reg S1;
    reg RST;
    wire [15:0] LED;
    wire [7:0] SEG;
    wire [3:0] AN;
    
    // 实例化顶层模块
    SEQ_Display uut (
        .CLK(CLK),
        .S1(S1),
        .RST(RST),
        .LED(LED),
        .SEG(SEG),
        .AN(AN)
    );
    
    // 时钟生成
    initial begin
        CLK = 0;
        forever #5 CLK = ~CLK; // 100MHz时钟
    end
    
    // 测试序列
    initial begin
        // 初始化
        S1 = 0;
        RST = 1;
        
        // 复位
        #100;
        RST = 0;
        
        // 等待一段时间
        #100;
        
        // 模拟按钮按下（单步执行）
        repeat(10) begin
            #1000000; // 等待1ms
            S1 = 1;
            #100000;  // 按钮按下100us
            S1 = 0;
            #1000000; // 等待1ms
        end
        
        // 结束仿真
        #10000000;
        $finish;
    end
    
    // 监控输出
    initial begin
        $monitor("Time=%0t, LED=%h, SEG=%b, AN=%b", $time, LED, SEG, AN);
    end
    
endmodule
