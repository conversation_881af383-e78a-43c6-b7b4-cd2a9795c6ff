<?xml version="1.0" encoding="UTF-8" ?>
<document>
<!--The data in this file is primarily intended for consumption by Xilinx tools.
The structure and the elements are likely to change over the next few releases.
This means code written to parse this file will need to be revisited each subsequent release.-->
<application name="pa" timeStamp="Tue Jun  3 23:07:54 2025">
<section name="Project Information" visible="false">
<property name="ProjectID" value="59d10d834f1c4b25a7b5cfa1e91582cc" type="ProjectID"/>
<property name="ProjectIteration" value="1" type="ProjectIteration"/>
</section>
<section name="PlanAhead Usage" visible="true">
<item name="Project Data">
<property name="SrcSetCount" value="1" type="SrcSetCount"/>
<property name="ConstraintSetCount" value="1" type="ConstraintSetCount"/>
<property name="DesignMode" value="RTL" type="DesignMode"/>
<property name="SynthesisStrategy" value="Vivado Synthesis Defaults" type="SynthesisStrategy"/>
<property name="ImplStrategy" value="Vivado Implementation Defaults" type="ImplStrategy"/>
</item>
<item name="Java Command Handlers">
<property name="AddSources" value="1" type="JavaHandler"/>
<property name="SimulationRelaunch" value="1" type="JavaHandler"/>
<property name="SimulationRun" value="1" type="JavaHandler"/>
<property name="SimulationRunAll" value="6" type="JavaHandler"/>
</item>
<item name="Gui Handlers">
<property name="FlowNavigatorTreePanel_FLOW_NAVIGATOR_TREE" value="2" type="GuiHandlerData"/>
<property name="MainToolbarMgr_RUN" value="1" type="GuiHandlerData"/>
<property name="OptionsView_CLOSE" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_LIVE_RUN_ALL" value="5" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RELAUNCH" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RUN_BEHAVIORAL" value="1" type="GuiHandlerData"/>
<property name="PAViews_PROJECT_SUMMARY" value="1" type="GuiHandlerData"/>
<property name="RDIViews_WAVEFORM_VIEWER" value="5" type="GuiHandlerData"/>
<property name="SimulationObjectsPanel_SIMULATION_OBJECTS_TREE_TABLE" value="27" type="GuiHandlerData"/>
<property name="SimulationScopesPanel_SIMULATE_SCOPE_TABLE" value="6" type="GuiHandlerData"/>
<property name="SrcChooserPanel_ADD_HDL_AND_NETLIST_FILES_TO_YOUR_PROJECT" value="1" type="GuiHandlerData"/>
<property name="SrcChooserPanel_ADD_OR_CREATE_SOURCE_FILE" value="1" type="GuiHandlerData"/>
<property name="WaveformNameTree_WAVEFORM_NAME_TREE" value="7" type="GuiHandlerData"/>
</item>
<item name="Other">
<property name="GuiMode" value="43" type="GuiMode"/>
<property name="BatchMode" value="0" type="BatchMode"/>
<property name="TclMode" value="34" type="TclMode"/>
</item>
</section>
</application>
</document>
