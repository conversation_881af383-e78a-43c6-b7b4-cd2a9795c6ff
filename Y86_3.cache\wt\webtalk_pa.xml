<?xml version="1.0" encoding="UTF-8" ?>
<document>
<!--The data in this file is primarily intended for consumption by Xilinx tools.
The structure and the elements are likely to change over the next few releases.
This means code written to parse this file will need to be revisited each subsequent release.-->
<application name="pa" timeStamp="Wed Jun  4 01:09:43 2025">
<section name="Project Information" visible="false">
<property name="ProjectID" value="59d10d834f1c4b25a7b5cfa1e91582cc" type="ProjectID"/>
<property name="ProjectIteration" value="4" type="ProjectIteration"/>
</section>
<section name="PlanAhead Usage" visible="true">
<item name="Project Data">
<property name="SrcSetCount" value="1" type="SrcSetCount"/>
<property name="ConstraintSetCount" value="1" type="ConstraintSetCount"/>
<property name="DesignMode" value="RTL" type="DesignMode"/>
<property name="SynthesisStrategy" value="Vivado Synthesis Defaults" type="SynthesisStrategy"/>
<property name="ImplStrategy" value="Vivado Implementation Defaults" type="ImplStrategy"/>
</item>
<item name="Java Command Handlers">
<property name="AddSources" value="2" type="JavaHandler"/>
<property name="AutoConnectTarget" value="1" type="JavaHandler"/>
<property name="CloseProject" value="1" type="JavaHandler"/>
<property name="LaunchProgramFpga" value="1" type="JavaHandler"/>
<property name="NewProject" value="1" type="JavaHandler"/>
<property name="OpenHardwareManager" value="6" type="JavaHandler"/>
<property name="OpenRecentTarget" value="5" type="JavaHandler"/>
<property name="ProgramDevice" value="2" type="JavaHandler"/>
<property name="RunBitgen" value="3" type="JavaHandler"/>
<property name="RunSynthesis" value="1" type="JavaHandler"/>
<property name="SaveDesign" value="2" type="JavaHandler"/>
<property name="ShowView" value="1" type="JavaHandler"/>
<property name="SimulationRelaunch" value="1" type="JavaHandler"/>
<property name="SimulationRun" value="1" type="JavaHandler"/>
<property name="SimulationRunAll" value="6" type="JavaHandler"/>
<property name="TimingConstraintsWizard" value="1" type="JavaHandler"/>
<property name="ViewTaskSynthesis" value="1" type="JavaHandler"/>
</item>
<item name="Gui Handlers">
<property name="BaseDialog_OK" value="9" type="GuiHandlerData"/>
<property name="BaseDialog_YES" value="3" type="GuiHandlerData"/>
<property name="CmdMsgDialog_OK" value="1" type="GuiHandlerData"/>
<property name="CreateConstraintsFilePanel_FILE_LOCATION" value="1" type="GuiHandlerData"/>
<property name="CreateConstraintsFilePanel_FILE_NAME" value="1" type="GuiHandlerData"/>
<property name="FPGAChooser_FPGA_TABLE" value="1" type="GuiHandlerData"/>
<property name="FileSetPanel_FILE_SET_PANEL_TREE" value="12" type="GuiHandlerData"/>
<property name="FlowNavigatorTreePanel_FLOW_NAVIGATOR_TREE" value="18" type="GuiHandlerData"/>
<property name="MainMenuMgr_EDIT" value="6" type="GuiHandlerData"/>
<property name="MainMenuMgr_FILE" value="6" type="GuiHandlerData"/>
<property name="MainMenuMgr_FLOW" value="2" type="GuiHandlerData"/>
<property name="MainMenuMgr_PROJECT" value="1" type="GuiHandlerData"/>
<property name="MainToolbarMgr_RUN" value="1" type="GuiHandlerData"/>
<property name="OptionsView_CLOSE" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_CONNECT_TARGET" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_NEW_PROJECT" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_OPEN_HARDWARE_MANAGER" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_LIVE_RUN_ALL" value="5" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RELAUNCH" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RUN_BEHAVIORAL" value="1" type="GuiHandlerData"/>
<property name="PAViews_CODE" value="3" type="GuiHandlerData"/>
<property name="PAViews_PROJECT_SUMMARY" value="3" type="GuiHandlerData"/>
<property name="ProgramFpgaDialog_PROGRAM" value="1" type="GuiHandlerData"/>
<property name="ProjectNameChooser_PROJECT_NAME" value="1" type="GuiHandlerData"/>
<property name="ProjectTab_RELOAD" value="1" type="GuiHandlerData"/>
<property name="RDIViews_WAVEFORM_VIEWER" value="5" type="GuiHandlerData"/>
<property name="SignalTreePanel_SIGNAL_TREE_TABLE" value="48" type="GuiHandlerData"/>
<property name="SimulationObjectsPanel_SIMULATION_OBJECTS_TREE_TABLE" value="27" type="GuiHandlerData"/>
<property name="SimulationScopesPanel_SIMULATE_SCOPE_TABLE" value="6" type="GuiHandlerData"/>
<property name="SrcChooserPanel_ADD_HDL_AND_NETLIST_FILES_TO_YOUR_PROJECT" value="2" type="GuiHandlerData"/>
<property name="SrcChooserPanel_ADD_OR_CREATE_SOURCE_FILE" value="2" type="GuiHandlerData"/>
<property name="SyntheticaGettingStartedView_RECENT_PROJECTS" value="2" type="GuiHandlerData"/>
<property name="WaveformNameTree_WAVEFORM_NAME_TREE" value="7" type="GuiHandlerData"/>
</item>
<item name="Other">
<property name="GuiMode" value="46" type="GuiMode"/>
<property name="BatchMode" value="0" type="BatchMode"/>
<property name="TclMode" value="37" type="TclMode"/>
</item>
</section>
</application>
</document>
