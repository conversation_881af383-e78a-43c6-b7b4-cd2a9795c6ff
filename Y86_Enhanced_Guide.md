# Y86处理器增强版 - 单步执行 + LED显示 + 蜂鸣器

## 系统概述

增强版Y86处理器结合了单步执行控制、LED状态显示和蜂鸣器提示功能。通过S1按钮控制单步执行，LED实时显示处理器状态，程序执行完毕后蜂鸣器发出提示音。

## 硬件接口

### 输入信号
- **CLK**: 系统时钟输入 (100MHz) - 用于按钮防抖和蜂鸣器控制
- **S1**: 单步执行按钮 - 每次按下执行一条指令
- **RST**: 复位按钮 - 重置处理器到初始状态

### 输出信号
- **LED[15:0]**: 16个LED显示处理器状态和最终结果
- **BUZZER**: 蜂鸣器输出 - 程序停机时发出提示音

## LED显示说明

### 程序运行期间
LED显示当前执行状态：
- **LED[15:12]**: 当前指令码 (icode)
  - 0x0: halt    停机指令
  - 0x1: nop     空操作
  - 0x2: rrmovw  寄存器传送
  - 0x3: irmovw  立即数传送
  - 0x4: rmmovw  寄存器到内存
  - 0x5: mrmovw  内存到寄存器
  - 0x6: OPw     算术运算
  - 0x7: jXX     跳转指令
  - 0x8: call    函数调用
  - 0x9: ret     函数返回
  - 0xA: pushw   压栈
  - 0xB: popw    出栈

- **LED[11:10]**: 处理器状态 (Stat)
  - 00: AOK (正常运行)
  - 01: HLT (停机)
  - 10: ADR (地址错误)
  - 11: INS (指令错误)

- **LED[9:0]**: 程序计数器PC的低10位

### 程序执行完毕后
LED显示最终计算结果：
- **LED[15:0]**: 数组元素求和的结果 (0xABCD = 43981)

## 蜂鸣器功能

### 触发条件
- 程序执行到halt指令并停机时自动触发
- 检测处理器状态从AOK变为HLT的瞬间

### 声音特性
- **频率**: 约1KHz，清晰可听
- **持续时间**: 约0.5秒
- **音调**: 单一频率的"嘀"声

## 开发板测试步骤

### 步骤1: Vivado项目设置
1. **创建项目**
   ```
   - 打开Vivado 2018.3或更高版本
   - 创建新项目，选择Minisys开发板
   - 添加源文件：src/SEQ16_Enhanced.v
   - 设置SEQ_Display_Enhanced为顶层模块
   ```

2. **引脚约束** (根据您的开发板手册添加)
   ```
   需要为以下信号分配引脚：
   - CLK: 系统时钟输入
   - S1: 单步按钮
   - RST: 复位按钮
   - LED[15:0]: 16个LED输出
   - BUZZER: 蜂鸣器输出
   ```

3. **综合与实现**
   ```
   - 运行Synthesis（综合）
   - 运行Implementation（实现）
   - 生成Bitstream（比特流）
   ```

### 步骤2: 单步执行测试
1. **硬件连接**
   ```
   - 连接Minisys开发板到电脑
   - 确保开发板电源开关打开
   - 下载比特流文件到开发板
   ```

2. **复位操作**
   ```
   - 按下RST按钮复位处理器
   - 观察LED显示：PC=000, icode=3, 状态=00
   - 此时程序准备从第一条指令开始执行
   ```

3. **单步执行过程**
   ```
   按下S1按钮，观察每一步的变化：
   
   第1步: irmovw $0x0080, %sp
   - LED[15:12] = 0011 (icode=3)
   - LED[11:10] = 00 (状态=AOK)
   - LED[9:0] = 000000000 (PC=000)
   
   第2步: call main
   - LED[15:12] = 1000 (icode=8)
   - LED[9:0] = 000000100 (PC=004)
   
   第3步: irmovw $0x0000, %di
   - LED[15:12] = 0011 (icode=3)
   - LED[9:0] = 000001000 (PC=008)
   
   ... 继续按S1观察每条指令的执行
   ```

4. **循环执行观察**
   ```
   在sum函数的循环部分，会看到：
   - mrmovw指令 (icode=5): 从内存读取数组元素
   - addw指令 (icode=6): 累加计算
   - jne指令 (icode=7): 条件跳转
   - PC在0x023-0x02D之间循环4次
   ```

5. **程序结束**
   ```
   最后几步：
   - ret指令返回main函数
   - ret指令返回主程序
   - halt指令停机
   - LED显示最终结果：0xABCD
   - 蜂鸣器发出"嘀"声提示程序结束
   ```

## 预期测试结果

### 正确的执行流程
1. **总指令数**: 约35条指令
2. **循环次数**: 4次 (处理4个数组元素)
3. **最终结果**: 0xABCD (43981)
4. **蜂鸣器**: 程序停机时响起

### LED最终显示
- **二进制**: 1010 1011 1100 1101
- **LED状态**: 亮灭亮灭 亮灭亮亮 亮亮灭灭 亮亮灭亮

## 故障排除

### 常见问题
1. **按钮无响应**: 检查S1按钮连接和防抖逻辑
2. **LED显示异常**: 检查时钟信号和复位逻辑
3. **蜂鸣器不响**: 检查蜂鸣器引脚约束和驱动逻辑
4. **程序不停机**: 检查halt指令和状态检测

### 调试建议
1. 使用RST按钮重新开始
2. 逐步观察LED的指令码变化
3. 验证PC值的递增是否正确
4. 确认最终结果为0xABCD

## 技术特点

✅ **精确单步控制**: 每次按钮按下只执行一条指令  
✅ **实时状态显示**: LED立即反映处理器状态变化  
✅ **音频提示**: 蜂鸣器提供程序完成的听觉反馈  
✅ **完整验证**: 验证Y86处理器的所有核心功能  
✅ **教学友好**: 适合学习和理解处理器工作原理  

这个增强版本为Y86处理器提供了完整的交互式学习体验，让您能够深入理解每条指令的执行过程！
