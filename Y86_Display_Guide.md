# Y86处理器单步执行LED展示系统

## 系统概述

本系统将您的Y86处理器改造为单步执行模式，通过Minisys开发板上的16个LED实时显示处理器的执行状态。每次按下S1按钮，处理器执行一条指令，LED立即更新显示结果。

## 硬件接口

### 输入信号
- **CLK**: 系统时钟输入 (100MHz) - 用于按钮防抖
- **S1**: 单步执行按钮 - 每次按下执行一条指令
- **RST**: 复位按钮 - 重置处理器到初始状态

### 输出信号
- **LED[15:0]**: 16个LED显示处理器状态信息

## LED显示说明

LED从高位到低位的含义：
- **LED[15:12]**: 当前指令码 (icode)
  - 0x0: halt    停机指令
  - 0x1: nop     空操作
  - 0x2: rrmovw  寄存器传送
  - 0x3: irmovw  立即数传送
  - 0x4: rmmovw  寄存器到内存
  - 0x5: mrmovw  内存到寄存器
  - 0x6: OPw     算术运算
  - 0x7: jXX     跳转指令
  - 0x8: call    函数调用
  - 0x9: ret     函数返回
  - 0xA: pushw   压栈
  - 0xB: popw    出栈

- **LED[11:10]**: 处理器状态 (Stat)
  - 00: AOK (正常运行)
  - 01: HLT (停机)
  - 10: ADR (地址错误)
  - 11: INS (指令错误)

- **LED[9:2]**: 程序计数器PC的低8位
- **LED[1:0]**: 保留位 (固定为00)

## 使用步骤

### 1. Vivado项目设置
1. 打开Vivado，创建新项目
2. 选择Minisys开发板作为目标器件
3. 添加源文件：`src/SEQ16.v`
4. 设置 `SEQ_Display` 为顶层模块
5. 运行综合和实现
6. 生成比特流文件

### 2. 开发板操作
1. 连接Minisys开发板到电脑
2. 下载比特流到开发板
3. **复位操作**：按下RST按钮
   - 观察LED显示：PC=00, icode=3, 状态=00
4. **单步执行**：按下S1按钮
   - 每次按下执行一条指令
   - 观察LED的变化，了解程序执行过程

### 3. 观察要点
- **指令执行**：LED[15:12]显示当前指令类型
- **程序进度**：LED[9:2]显示PC值的变化
- **运行状态**：LED[11:10]显示处理器是否正常

## 单步执行原理

### 按钮防抖机制
- S1按钮经过三级同步器和计数器防抖
- 防抖时间约10ms，确保按钮信号稳定可靠
- 只在按钮按下的上升沿产生脉冲

### 脉冲生成
- 每次按下S1按钮产生一个固定宽度的时钟脉冲
- 该脉冲直接驱动Y86处理器的时钟输入
- 确保每次按钮按下只执行一条指令

## 测试程序说明

内置的测试程序功能：
1. **初始化**: 设置栈指针到0x0080
2. **函数调用**: 调用main函数
3. **数组处理**: 计算数组元素的和
4. **循环执行**: 包含跳转和条件判断
5. **结果返回**: 最终停机

## 关键特性

✅ **可靠的单步执行**: 每次按钮按下确保只执行一条指令
✅ **实时状态显示**: LED立即反映处理器状态变化
✅ **简单易用**: 只需要S1和RST两个按钮
✅ **完整功能**: 支持Y86指令集的所有指令类型

## 注意事项

1. **按钮操作**: 正常按下释放即可，不要长按
2. **复位操作**: 程序执行完毕后可按RST重新开始
3. **LED观察**: 重点关注高位LED的指令码和状态位变化

这个设计让您能够清楚地观察Y86处理器的每一步执行过程，是学习和调试处理器的理想工具。
