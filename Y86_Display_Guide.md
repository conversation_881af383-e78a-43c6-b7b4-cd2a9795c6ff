# Y86处理器单步执行展示系统使用指南

## 系统概述

本系统将您的Y86处理器改造为单步执行模式，通过Minisys开发板上的LED和七段数码管实时显示处理器的执行状态。

## 硬件连接

### 输入信号
- **CLK**: 系统时钟输入 (100MHz)
- **S1**: 单步执行按钮
- **RST**: 复位按钮

### 输出信号
- **LED[15:0]**: 16个LED显示ALU计算结果(valE)
- **SEG[7:0]**: 七段数码管段选信号
- **AN[3:0]**: 七段数码管位选信号

## 显示说明

### LED显示
- LED[15:0]显示当前指令执行后的ALU结果(valE)
- LED亮表示该位为1，LED灭表示该位为0

### 七段数码管显示
数码管从右到左显示：
1. **最右侧**: PC的低4位 (PC[3:0])
2. **右侧第二位**: PC的高4位 (PC[7:4])  
3. **左侧第二位**: 当前指令码 (icode)
4. **最左侧**: 处理器状态 (Stat)
   - 0: AOK (正常)
   - 1: HLT (停机)
   - 2: ADR (地址错误)
   - 3: INS (指令错误)

## 操作步骤

### 1. 在Vivado中创建项目
```
1. 打开Vivado
2. 创建新项目，选择Minisys开发板
3. 添加源文件：src/SEQ16.v
4. 添加约束文件：src/minisys_constraints.xdc
5. 设置SEQ_Display为顶层模块
```

### 2. 综合与实现
```
1. 运行综合 (Synthesis)
2. 运行实现 (Implementation)
3. 生成比特流 (Generate Bitstream)
```

### 3. 下载到开发板
```
1. 连接Minisys开发板到电脑
2. 在Vivado中选择"Open Hardware Manager"
3. 连接到开发板
4. 下载比特流文件
```

### 4. 单步执行操作
```
1. 按下RST按钮复位处理器
2. 观察数码管显示PC=00，icode=3，状态=0
3. 按下S1按钮执行第一条指令
4. 观察LED和数码管的变化
5. 继续按S1按钮逐步执行程序
```

## 按钮工作原理

### 防抖机制
- S1按钮经过硬件防抖处理，消除机械抖动
- 防抖时间约为10ms，确保按钮信号稳定

### 脉冲生成
- 每次按下S1按钮产生一个时钟脉冲
- 该脉冲驱动Y86处理器执行一条指令
- 不需要连续的系统时钟，完全由按钮控制

## 程序执行流程

当前加载的测试程序执行以下操作：
1. 初始化栈指针 (%sp = 0x0080)
2. 调用main函数
3. 设置数组地址和计数器
4. 调用sum函数计算数组和
5. 循环读取数组元素并累加
6. 返回结果

## 调试技巧

### 观察要点
1. **PC变化**: 观察程序计数器是否按预期递增
2. **指令码**: 确认当前执行的指令类型
3. **ALU结果**: 通过LED观察计算结果
4. **状态码**: 监控处理器是否正常运行

### 常见问题
1. **按钮无响应**: 检查约束文件中的引脚分配
2. **显示异常**: 确认时钟信号正常
3. **程序不执行**: 检查复位信号和初始PC值

## 扩展功能

可以进一步扩展的功能：
1. 添加更多LED显示寄存器值
2. 使用LCD显示更详细的状态信息
3. 添加断点功能
4. 实现连续执行模式切换

## 技术细节

### 时钟域
- 系统时钟: 100MHz (用于防抖和数码管刷新)
- 处理器时钟: 单步脉冲 (由S1按钮产生)

### 资源使用
- LUT: 约200个
- FF: 约150个
- BRAM: 2个 (指令存储器和数据存储器)

这个设计实现了完全的单步控制，让您可以清楚地观察Y86处理器的每一步执行过程。
