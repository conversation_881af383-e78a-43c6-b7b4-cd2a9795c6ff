# Y86处理器连续执行LED结果展示系统

## 系统概述

本系统让Y86处理器连续执行内置的测试程序，并通过Minisys开发板上的16个LED显示程序执行过程和最终结果。程序执行完毕后，LED将显示计算得到的最终结果。

## 硬件接口

### 输入信号
- **CLK**: 系统时钟输入 (100MHz) - 经过分频后驱动处理器
- **RST**: 复位按钮 - 重置处理器并重新开始执行

### 输出信号
- **LED[15:0]**: 16个LED显示执行状态和最终结果

## LED显示说明

### 程序运行期间
LED显示当前执行状态：
- **LED[15:12]**: 当前指令码 (icode)
- **LED[11:10]**: 处理器状态 (Stat)
- **LED[9:0]**: 程序计数器PC的低10位

### 程序执行完毕后
LED显示最终计算结果：
- **LED[15:0]**: 数组元素求和的结果 (16位二进制数)

## 测试程序说明

内置程序功能：计算数组元素的和
- **数组数据**: [13, 192, 2816, 40960]
- **预期结果**: 13 + 192 + 2816 + 40960 = 43981 (0xABCD)
- **存储位置**: 结果保存在%ax寄存器中

## 开发板测试步骤

### 步骤1: Vivado项目设置
1. **创建项目**
   ```
   - 打开Vivado 2018.3或更高版本
   - 创建新项目，选择Minisys开发板
   - 添加源文件：src/SEQ16.v
   - 设置SEQ_Display为顶层模块
   ```

2. **综合与实现**
   ```
   - 运行Synthesis（综合）
   - 运行Implementation（实现）
   - 生成Bitstream（比特流）
   ```

### 步骤2: 开发板测试
1. **硬件连接**
   ```
   - 连接Minisys开发板到电脑USB端口
   - 确保开发板电源开关打开
   - 观察电源指示灯亮起
   ```

2. **下载程序**
   ```
   - 在Vivado中打开Hardware Manager
   - 连接到开发板
   - 下载生成的比特流文件
   ```

3. **观察执行过程**
   ```
   - 下载完成后，程序自动开始执行
   - 观察LED的变化：
     * 初始阶段：LED显示指令码和PC值的变化
     * 执行过程：可以看到不同的指令码（3,8,6,7等）
     * 最终结果：程序停机后LED显示计算结果
   ```

4. **验证结果**
   ```
   - 程序执行完毕后，LED应显示：0xABCD (43981)
   - 对应二进制：1010 1011 1100 1101
   - LED15-12: 1010, LED11-8: 1011, LED7-4: 1100, LED3-0: 1101
   ```

5. **重新测试**
   ```
   - 按下RST按钮重置处理器
   - 程序将重新开始执行
   - 再次观察整个执行过程
   ```

## 执行时序说明

### 时钟分频机制
- 系统时钟100MHz经过分频器分频到2Hz
- 处理器以2Hz的频率执行指令
- 每0.5秒执行一条指令，便于观察执行过程

### 程序执行流程
1. **初始化阶段** (约2秒)
   - 设置栈指针 (%sp = 0x0080)
   - 调用main函数
   - 设置数组地址和计数器

2. **计算阶段** (约6秒)
   - 循环读取数组元素
   - 累加计算求和
   - 条件判断和跳转

3. **结束阶段** (约1秒)
   - 函数返回
   - 程序停机
   - 显示最终结果

## 预期观察现象

### LED变化过程
1. **开始执行**: LED快速变化，显示不同的指令码
2. **循环计算**: LED[15:12]主要显示5(mrmovw)、6(addw)、7(jne)
3. **程序停机**: LED稳定显示最终结果0xABCD

### 结果验证
- **正确结果**: LED显示0xABCD (二进制: 1010101111001101)
- **LED状态**: 从左到右应该是: 亮灭亮灭 亮灭亮亮 亮亮灭灭 亮亮灭亮

## 故障排除

### 常见问题
1. **LED不亮**: 检查开发板电源和USB连接
2. **LED乱闪**: 检查时钟信号和复位信号
3. **结果错误**: 检查程序下载是否成功
4. **程序不停机**: 检查halt指令和状态检测逻辑

### 调试建议
1. 使用RST按钮重新开始执行
2. 观察LED变化的规律性
3. 验证最终结果是否为0xABCD
4. 如有问题，重新下载比特流文件

## 技术特点

✅ **自动执行**: 程序下载后自动开始运行
✅ **可视化显示**: LED实时显示执行状态和结果
✅ **简单操作**: 只需要RST按钮控制
✅ **完整验证**: 验证Y86处理器的所有核心功能

这个设计让您能够直观地观察Y86处理器的完整执行过程，并验证计算结果的正确性。
