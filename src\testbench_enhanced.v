`timescale 1ns / 1ps

// 增强版Y86处理器测试台 - 验证单步执行和蜂鸣器功能
module testbench_enhanced();
    reg CLK;
    reg S1;
    reg RST;
    wire [15:0] LED;
    wire BUZZER;
    
    // 实例化增强版顶层模块
    SEQ_Display_Enhanced uut (
        .CLK(CLK),
        .S1(S1),
        .RST(RST),
        .LED(LED),
        .BUZZER(BUZZER)
    );
    
    // 时钟生成 - 100MHz
    initial begin
        CLK = 0;
        forever #5 CLK = ~CLK;
    end
    
    // 测试序列
    initial begin
        // 初始化
        S1 = 0;
        RST = 1;
        #100;
        RST = 0;
        
        $display("=== Y86处理器增强版测试开始 ===");
        $display("时间=%0t: 复位完成，开始单步执行", $time);
        
        // 等待一段时间让系统稳定
        #1000;
        
        // 模拟单步执行 - 执行前几条指令
        repeat(10) begin
            #1000000; // 等待1ms
            $display("时间=%0t: 按下S1按钮", $time);
            S1 = 1;
            #100000;  // 按钮按下100us
            S1 = 0;
            #1000000; // 等待1ms
            $display("时间=%0t: LED=0x%h", $time, LED);
        end
        
        // 快速执行剩余指令直到程序停机
        $display("=== 快速执行剩余指令 ===");
        repeat(30) begin
            #500000; // 缩短等待时间
            S1 = 1;
            #50000;
            S1 = 0;
            #500000;
            
            // 检查是否停机
            if (LED == 16'hABCD) begin
                $display("时间=%0t: 程序执行完毕，结果正确: 0x%h", $time, LED);
                break;
            end
        end
        
        // 等待蜂鸣器响起
        $display("=== 等待蜂鸣器提示 ===");
        #60000000; // 等待60ms观察蜂鸣器
        
        if (BUZZER) begin
            $display("SUCCESS: 蜂鸣器正常工作");
        end else begin
            $display("WARNING: 蜂鸣器可能未正常工作");
        end
        
        // 验证最终结果
        if (LED == 16'hABCD) begin
            $display("SUCCESS: 最终结果正确: 0x%h (期望: 0xABCD)", LED);
        end else begin
            $display("ERROR: 最终结果错误: 0x%h (期望: 0xABCD)", LED);
        end
        
        $display("=== 测试完成 ===");
        $finish;
    end
    
    // 监控LED变化
    always @(LED) begin
        $display("LED变化: 时间=%0t, LED=0x%h, 指令码=%h, 状态=%h, PC=%h", 
                 $time, LED, LED[15:12], LED[11:10], LED[9:0]);
    end
    
    // 监控蜂鸣器
    always @(BUZZER) begin
        if (BUZZER) begin
            $display("蜂鸣器开始响起: 时间=%0t", $time);
        end else begin
            $display("蜂鸣器停止: 时间=%0t", $time);
        end
    end
    
    // 超时保护
    initial begin
        #200000000; // 200ms超时
        $display("ERROR: 测试超时");
        $finish;
    end
    
endmodule
