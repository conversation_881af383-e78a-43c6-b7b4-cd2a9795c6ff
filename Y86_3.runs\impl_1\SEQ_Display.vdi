#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Wed Jun  4 00:32:16 2025
# Process ID: 1704
# Current directory: E:/YS/Y86_3/Y86_3.runs/impl_1
# Command line: vivado.exe -log SEQ_Display.vdi -applog -product Vivado -messageDb vivado.pb -mode batch -source SEQ_Display.tcl -notrace
# Log file: E:/YS/Y86_3/Y86_3.runs/impl_1/SEQ_Display.vdi
# Journal file: E:/YS/Y86_3/Y86_3.runs/impl_1\vivado.jou
#-----------------------------------------------------------
source SEQ_Display.tcl -notrace
Command: open_checkpoint E:/YS/Y86_3/Y86_3.runs/impl_1/SEQ_Display.dcp

Starting open_checkpoint Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.020 . Memory (MB): peak = 250.980 ; gain = 0.000
INFO: [Netlist 29-17] Analyzing 536 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a100tfgg484-1
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Timing 38-478] Restoring timing data from binary archive.
INFO: [Timing 38-479] Binary timing data restore complete.
INFO: [Project 1-856] Restoring constraints from binary archive.
INFO: [Project 1-853] Binary constraint restore complete.
Reading XDEF placement.
Reading placer database...
Reading XDEF routing.
Read XDEF File: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.094 . Memory (MB): peak = 1156.609 ; gain = 0.000
Restored from archive | CPU: 0.000000 secs | Memory: 0.000000 MB |
Finished XDEF File Restore: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.095 . Memory (MB): peak = 1156.609 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1156.609 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

INFO: [Project 1-604] Checkpoint was created with Vivado v2018.3 (64-bit) build 2405991
open_checkpoint: Time (s): cpu = 00:00:11 ; elapsed = 00:00:13 . Memory (MB): peak = 1156.609 ; gain = 905.629
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.341 . Memory (MB): peak = 1156.609 ; gain = 0.000

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: 12f8f76c2

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.101 . Memory (MB): peak = 1245.535 ; gain = 88.926

Starting Logic Optimization Task

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: 177deb628

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.194 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Opt 31-389] Phase Retarget created 0 cells and removed 3 cells

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Phase 2 Constant propagation | Checksum: 177deb628

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.244 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Opt 31-389] Phase Constant propagation created 0 cells and removed 0 cells

Phase 3 Sweep
Phase 3 Sweep | Checksum: 16dcbf981

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.309 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 0 cells

Phase 4 BUFG optimization
Phase 4 BUFG optimization | Checksum: 16dcbf981

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.353 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 5 Shift Register Optimization
Phase 5 Shift Register Optimization | Checksum: 1884b0df3

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.532 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 6 Post Processing Netlist
Phase 6 Post Processing Netlist | Checksum: 1884b0df3

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.554 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |               0  |               3  |                                              0  |
|  Constant propagation         |               0  |               0  |                                              0  |
|  Sweep                        |               0  |               0  |                                              0  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                              0  |
-------------------------------------------------------------------------------------------------------------------------



Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.010 . Memory (MB): peak = 1329.867 ; gain = 0.000
Ending Logic Optimization Task | Checksum: 1884b0df3

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.572 . Memory (MB): peak = 1329.867 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
Ending Power Optimization Task | Checksum: 1884b0df3

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 1329.867 ; gain = 0.000

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: 1884b0df3

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1329.867 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1329.867 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: 1884b0df3

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
27 Infos, 0 Warnings, 0 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.022 . Memory (MB): peak = 1329.867 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1329.867 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/YS/Y86_3/Y86_3.runs/impl_1/SEQ_Display_opt.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file SEQ_Display_drc_opted.rpt -pb SEQ_Display_drc_opted.pb -rpx SEQ_Display_drc_opted.rpx
Command: report_drc -file SEQ_Display_drc_opted.rpt -pb SEQ_Display_drc_opted.pb -rpx SEQ_Display_drc_opted.rpx
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'D:/Xilinx/Vivado/2018.3/data/ip'.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/YS/Y86_3/Y86_3.runs/impl_1/SEQ_Display_drc_opted.rpt.
report_drc completed successfully
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1329.867 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: 12b621b0c

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1329.867 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1329.867 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: 1095395e7

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.724 . Memory (MB): peak = 1337.078 ; gain = 7.211

Phase 1.3 Build Placer Netlist Model
Phase 1.3 Build Placer Netlist Model | Checksum: 1d1f6e87e

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.867 . Memory (MB): peak = 1341.297 ; gain = 11.430

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 1d1f6e87e

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.875 . Memory (MB): peak = 1341.297 ; gain = 11.430
Phase 1 Placer Initialization | Checksum: 1d1f6e87e

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.879 . Memory (MB): peak = 1341.297 ; gain = 11.430

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 1d1f6e87e

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.889 . Memory (MB): peak = 1343.426 ; gain = 13.559
WARNING: [Place 46-29] place_design is not in timing mode. Skip physical synthesis in placer
Phase 2 Global Placement | Checksum: 193612170

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 1358.098 ; gain = 28.230

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 193612170

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 1358.098 ; gain = 28.230

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 18a699c43

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 1358.098 ; gain = 28.230

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 1a66c1f60

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1358.098 ; gain = 28.230

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 1a66c1f60

Time (s): cpu = 00:00:03 ; elapsed = 00:00:02 . Memory (MB): peak = 1358.098 ; gain = 28.230

Phase 3.5 Small Shape Detail Placement
Phase 3.5 Small Shape Detail Placement | Checksum: e8559243

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613

Phase 3.6 Re-assign LUT pins
Phase 3.6 Re-assign LUT pins | Checksum: e8559243

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613

Phase 3.7 Pipeline Register Optimization
Phase 3.7 Pipeline Register Optimization | Checksum: e8559243

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613
Phase 3 Detail Placement | Checksum: e8559243

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
Phase 4.1 Post Commit Optimization | Checksum: e8559243

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: e8559243

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: e8559243

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1363.480 ; gain = 0.000
Phase 4.4 Final Placement Cleanup | Checksum: 12ec7273e

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 12ec7273e

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613
Ending Placer Task | Checksum: ead84d65

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1363.480 ; gain = 33.613
INFO: [Common 17-83] Releasing license: Implementation
45 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
place_design completed successfully
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1363.480 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1369.305 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.309 . Memory (MB): peak = 1369.305 ; gain = 5.824
INFO: [Common 17-1381] The checkpoint 'E:/YS/Y86_3/Y86_3.runs/impl_1/SEQ_Display_placed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_io -file SEQ_Display_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.039 . Memory (MB): peak = 1369.305 ; gain = 0.000
INFO: [runtcl-4] Executing : report_utilization -file SEQ_Display_utilization_placed.rpt -pb SEQ_Display_utilization_placed.pb
INFO: [runtcl-4] Executing : report_control_sets -verbose -file SEQ_Display_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.013 . Memory (MB): peak = 1369.305 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: 40f540fb ConstDB: 0 ShapeSum: a9e30c6a RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 1782f5e20

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1526.270 ; gain = 156.965
Post Restoration Checksum: NetGraph: eae6e191 NumContArr: 8d487c8f Constraints: 0 Timing: 0

Phase 2 Router Initialization
INFO: [Route 35-64] No timing constraints were detected. The router will operate in resource-optimization mode.

Phase 2.1 Fix Topology Constraints
Phase 2.1 Fix Topology Constraints | Checksum: 1782f5e20

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1532.293 ; gain = 162.988

Phase 2.2 Pre Route Cleanup
Phase 2.2 Pre Route Cleanup | Checksum: 1782f5e20

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1532.293 ; gain = 162.988
 Number of Nodes with overlaps = 0
Phase 2 Router Initialization | Checksum: 0fd12e37

Time (s): cpu = 00:00:20 ; elapsed = 00:00:18 . Memory (MB): peak = 1552.094 ; gain = 182.789

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: d79dbd8e

Time (s): cpu = 00:00:21 ; elapsed = 00:00:19 . Memory (MB): peak = 1552.094 ; gain = 182.789

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 1192
 Number of Nodes with overlaps = 9
 Number of Nodes with overlaps = 0
Phase 4.1 Global Iteration 0 | Checksum: 15c2215a9

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789
Phase 4 Rip-up And Reroute | Checksum: 15c2215a9

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789

Phase 5 Delay and Skew Optimization
Phase 5 Delay and Skew Optimization | Checksum: 15c2215a9

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter
Phase 6.1 Hold Fix Iter | Checksum: 15c2215a9

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789
Phase 6 Post Hold Fix | Checksum: 15c2215a9

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 1.37877 %
  Global Horizontal Routing Utilization  = 1.46533 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Congestion Report
North Dir 1x1 Area, Max Cong = 50.4505%, No Congested Regions.
South Dir 1x1 Area, Max Cong = 46.8468%, No Congested Regions.
East Dir 1x1 Area, Max Cong = 54.4118%, No Congested Regions.
West Dir 1x1 Area, Max Cong = 51.4706%, No Congested Regions.

------------------------------
Reporting congestion hotspots
------------------------------
Direction: North
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: South
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: East
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0
Direction: West
----------------
Congested clusters found at Level 0
Effective congestion level: 0 Aspect Ratio: 1 Sparse Ratio: 0

Phase 7 Route finalize | Checksum: 15c2215a9

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: 15c2215a9

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: d7c424a4

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1552.094 ; gain = 182.789

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
58 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:00:24 ; elapsed = 00:00:21 . Memory (MB): peak = 1552.094 ; gain = 182.789
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1552.094 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 1552.094 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.421 . Memory (MB): peak = 1552.094 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'E:/YS/Y86_3/Y86_3.runs/impl_1/SEQ_Display_routed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file SEQ_Display_drc_routed.rpt -pb SEQ_Display_drc_routed.pb -rpx SEQ_Display_drc_routed.rpx
Command: report_drc -file SEQ_Display_drc_routed.rpt -pb SEQ_Display_drc_routed.pb -rpx SEQ_Display_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file E:/YS/Y86_3/Y86_3.runs/impl_1/SEQ_Display_drc_routed.rpt.
report_drc completed successfully
INFO: [runtcl-4] Executing : report_methodology -file SEQ_Display_methodology_drc_routed.rpt -pb SEQ_Display_methodology_drc_routed.pb -rpx SEQ_Display_methodology_drc_routed.rpx
Command: report_methodology -file SEQ_Display_methodology_drc_routed.rpt -pb SEQ_Display_methodology_drc_routed.pb -rpx SEQ_Display_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file E:/YS/Y86_3/Y86_3.runs/impl_1/SEQ_Display_methodology_drc_routed.rpt.
report_methodology completed successfully
INFO: [runtcl-4] Executing : report_power -file SEQ_Display_power_routed.rpt -pb SEQ_Display_power_summary_routed.pb -rpx SEQ_Display_power_routed.rpx
Command: report_power -file SEQ_Display_power_routed.rpt -pb SEQ_Display_power_summary_routed.pb -rpx SEQ_Display_power_routed.rpx
WARNING: [Power 33-232] No user defined clocks were found in the design!
Resolution: Please specify clocks using create_clock/create_generated_clock for sequential elements. For pure combinatorial circuits, please specify a virtual clock, otherwise the vectorless estimation might be inaccurate
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
70 Infos, 2 Warnings, 0 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [runtcl-4] Executing : report_route_status -file SEQ_Display_route_status.rpt -pb SEQ_Display_route_status.pb
INFO: [runtcl-4] Executing : report_timing_summary -max_paths 10 -file SEQ_Display_timing_summary_routed.rpt -pb SEQ_Display_timing_summary_routed.pb -rpx SEQ_Display_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
WARNING: [Timing 38-313] There are no user specified timing constraints. Timing constraints are needed for proper timing analysis.
INFO: [runtcl-4] Executing : report_incremental_reuse -file SEQ_Display_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [runtcl-4] Executing : report_clock_utilization -file SEQ_Display_clock_utilization_routed.rpt
INFO: [runtcl-4] Executing : report_bus_skew -warn_on_violation -file SEQ_Display_bus_skew_routed.rpt -pb SEQ_Display_bus_skew_routed.pb -rpx SEQ_Display_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
Command: write_bitstream -force SEQ_Display.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7a100t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a100t'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC CFGBVS-1] Missing CFGBVS and CONFIG_VOLTAGE Design Properties: Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 1 Warnings
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Writing bitstream ./SEQ_Display.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-120] WebTalk data collection is mandatory when using a WebPACK part without a full Vivado license. To see the specific WebTalk data collected for your design, open the usage_statistics_webtalk.html or usage_statistics_webtalk.xml file in the implementation directory.
INFO: [Common 17-83] Releasing license: Implementation
89 Infos, 4 Warnings, 0 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:11 ; elapsed = 00:00:11 . Memory (MB): peak = 2075.547 ; gain = 457.500
INFO: [Common 17-206] Exiting Vivado at Wed Jun  4 00:33:16 2025...
