`timescale 1ns / 1ps

// 简单的仿真测试台，用于验证设计
module test_simulation();
    reg CLK;
    reg RST;
    wire [15:0] LED;
    
    // 实例化顶层模块
    SEQ_Display uut (
        .CLK(CLK),
        .RST(RST),
        .LED(LED)
    );
    
    // 时钟生成 - 100MHz
    initial begin
        CLK = 0;
        forever #5 CLK = ~CLK;
    end
    
    // 测试序列
    initial begin
        // 初始化
        RST = 1;
        #100;
        RST = 0;
        
        // 等待程序执行完毕
        // 由于时钟分频，需要等待较长时间
        #1000000000; // 等待1秒（仿真时间）
        
        // 检查最终结果
        if (LED == 16'hABCD) begin
            $display("SUCCESS: Final result is correct: 0x%h", LED);
        end else begin
            $display("ERROR: Final result is incorrect: 0x%h (expected 0xABCD)", LED);
        end
        
        $finish;
    end
    
    // 监控LED变化
    always @(LED) begin
        $display("Time=%0t: LED = 0x%h", $time, LED);
    end
    
endmodule
